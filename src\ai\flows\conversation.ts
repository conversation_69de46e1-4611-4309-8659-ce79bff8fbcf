'use server';

/**
 * @fileOverview A flow for a simple conversation with the LifeSync assistant.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';

const ConversationInputSchema = z.object({
  history: z.array(z.string()).describe('Previous messages in the conversation'),
  message: z.string().describe("The user's message"),
});
export type ConversationInput = z.infer<typeof ConversationInputSchema>;

const ConversationOutputSchema = z
  .string()
  .describe('Assistant reply to the user');
export type ConversationOutput = z.infer<typeof ConversationOutputSchema>;

export async function converse(
  input: ConversationInput,
): Promise<ConversationOutput> {
  return conversationFlow(input);
}

const prompt = ai.definePrompt({
  name: 'conversationPrompt',
  input: { schema: ConversationInputSchema },
  output: { schema: ConversationOutputSchema },
  prompt:
    `You are a friendly productivity assistant. Continue the conversation based on the history and the user's latest message. Keep responses short and helpful.`,
});

const conversationFlow = ai.defineFlow(
  {
    name: 'conversationFlow',
    inputSchema: ConversationInputSchema,
    outputSchema: ConversationOutputSchema,
  },
  async ({ history, message }) => {
    const { output } = await prompt({ history: history.join('\n'), message });
    return output!;
  },
);
