
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Bo<PERSON> } from "lucide-react";
import Link from "next/link";

const personalityTypes = [
  { id: "coach", name: "Supportive Coach", description: "Encouraging and motivational." },
  { id: "advisor", name: "Analytical Advisor", description: "Data-driven and insightful." },
  { id: "companion", name: "Creative Companion", description: "Inspiring and imaginative." },
  { id: "guide", name: "Mindful Guide", description: "Calm and present-focused." },
  { id: "mentor", name: "Goal-Oriented Mentor", description: "Focused on achievement." },
];

export default function AIPersonalityPage() {
  return (
    <Card className="w-full shadow-xl">
      <CardHeader className="text-center">
        <Bot className="h-12 w-12 text-primary mx-auto mb-2" />
        <CardTitle className="text-3xl font-headline">Choose Your AI Companion</CardTitle>
        <CardDescription className="text-lg">
          Select a personality for your AI assistant to tailor its interactions.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <RadioGroup defaultValue="coach" className="space-y-3">
          {personalityTypes.map((type) => (
            <Label 
              key={type.id} 
              htmlFor={`personality-${type.id}`}
              className="flex items-center space-x-3 border p-4 rounded-md hover:bg-secondary/50 cursor-pointer has-[input:checked]:bg-secondary has-[input:checked]:border-primary"
            >
              <RadioGroupItem value={type.id} id={`personality-${type.id}`} />
              <div>
                <p className="font-semibold text-foreground">{type.name}</p>
                <p className="text-sm text-muted-foreground">{type.description}</p>
              </div>
            </Label>
          ))}
        </RadioGroup>
        <p className="text-xs text-muted-foreground text-center pt-2">
            You can change this later in your settings.
        </p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" asChild>
            <Link href="/onboarding/privacy">Back</Link>
        </Button>
        <Button asChild size="lg">
          <Link href="/onboarding/first-entry">Next: First Journal Entry</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
