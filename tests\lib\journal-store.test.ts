import { addJournalEntry, getLatestEntry } from '@/lib/journal-store'

beforeEach(() => {
  localStorage.clear()
})

test('new entry is persisted to localStorage', () => {
  const entry = addJournalEntry('Hello world')
  const stored = JSON.parse(localStorage.getItem('journalStoreEntries') || '[]')
  expect(stored[0]).toMatchObject(entry)
})

test('getLatestEntry reads from storage', () => {
  addJournalEntry('First')
  const second = addJournalEntry('Second')
  const latest = getLatestEntry()
  expect(latest).toEqual(second)
})
