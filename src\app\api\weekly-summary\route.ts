import { NextResponse } from 'next/server';
import { generateWeeklySummary } from '@/ai/flows/generate-weekly-summary';
import { requireAuth, createServerSupabaseClient, createErrorResponse } from '@/lib/auth-server';
import { aiRateLimiter, createRateLimitResponse } from '@/lib/rate-limit';
import { startOfWeek } from 'date-fns';

export async function GET() {
  try {
    // Require authentication
    const session = await requireAuth();
    
    // Apply rate limiting (more restrictive for weekly summaries)
    const rateLimitResult = await aiRateLimiter.check(`weekly-${session.user.id}`);
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult);
    }
    
    // Create authenticated Supabase client
    const supabase = await createServerSupabaseClient();
    
    // Get start of current week (Monday)
    const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
    
    // Fetch user's entries from this week
    const { data, error } = await supabase
      .from('entries')
      .select('blocks')
      .eq('user_id', session.user.id)
      .gte('created_at', weekStart.toISOString())
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Failed to load entries for weekly summary', error);
      throw new Error('Failed to load entries');
    }

    // Extract text from blocks
    const entries = (data || []).flatMap(entry => {
      if (!entry.blocks || !Array.isArray(entry.blocks)) return [];
      return entry.blocks
        .filter((block: any) => block.text && typeof block.text === 'string')
        .map((block: any) => block.text);
    });

    if (entries.length === 0) {
      return NextResponse.json({ 
        summary: 'No journal entries found for this week. Start writing to see your weekly summary!' 
      });
    }

    // Generate summary
    const summary = await generateWeeklySummary({ entries });
    
    // Optionally store the summary (uncomment if you want to persist summaries)
    // await supabase
    //   .from('weekly_summaries')
    //   .insert({ summary, user_id: session.user.id });
    
    return NextResponse.json({ summary });
  } catch (error) {
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    console.error('Failed to generate weekly summary', error);
    return createErrorResponse('Failed to generate weekly summary', 500);
  }
}
