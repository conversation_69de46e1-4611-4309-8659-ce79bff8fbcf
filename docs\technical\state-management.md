# State Management Technical Documentation

## Overview

LifeSync employs a pragmatic approach to state management, favoring simplicity over complexity. The application uses React's built-in state management capabilities combined with custom hooks to create a maintainable and understandable state architecture.

## State Management Philosophy

### Core Principles

1. **Local State First**: Use component state for UI-specific data
2. **Custom Hooks**: Encapsulate stateful logic and side effects
3. **Database as Source of Truth**: Minimize client-side state duplication
4. **Optimistic Updates**: Immediate UI feedback with eventual consistency
5. **Silent Failures**: Graceful degradation without error states

## State Architecture

### State Hierarchy

```
Application State
├── Global State
│   └── Authentication (Context API)
│
├── Feature State (Custom Hooks)
│   ├── Journal Entries (useEntries)
│   ├── Habits (useHabits)
│   ├── Blog Posts (useBlogPosts)
│   ├── Dashboard Layout (useDashboardLayout)
│   └── Public Layout (usePublicPageLayout)
│
├── UI State (Component State)
│   ├── Form Inputs
│   ├── Loading States
│   ├── Modal Visibility
│   └── Drag & Drop
│
└── Persistent State
    ├── Supabase (Primary)
    └── LocalStorage (Fallback)
```

## Global State Management

### Authentication Context

**Implementation**:
```typescript
const AuthContext = createContext<AuthContextType>()

interface AuthContextType {
  user: User | null
  session: Session | null
  signOut: () => Promise<void>
}
```

**Characteristics**:
- Only global state in the application
- Provides user session throughout app
- Wraps entire application tree
- Manages auth state transitions

**Usage Pattern**:
- Components access via `useAuth()` hook
- Triggers re-renders on auth changes
- Handles session persistence
- Coordinates with Supabase Auth

## Feature State Management

### Custom Hook Pattern

All feature-specific state follows a consistent pattern:

```typescript
function useFeature() {
  const [data, setData] = useState<Type[]>([])
  
  useEffect(() => {
    fetchData()
  }, [])
  
  const fetchData = async () => {
    const result = await supabase.from('table').select()
    if (result.data) setData(result.data)
  }
  
  const addItem = async (item: Type) => {
    setData(prev => [...prev, item]) // Optimistic update
    await supabase.from('table').insert(item)
  }
  
  return { data, addItem }
}
```

### Hook Implementations

#### 1. **useEntries** - Journal Management

**State Structure**:
- Merged array of remote and local entries
- Each entry marked with `isLocal` flag
- Sorted by creation date

**Key Features**:
- Dual source management (Supabase + localStorage)
- Unified interface for all entries
- Create operation with optimistic updates
- Silent error handling

**Data Flow**:
1. Fetch from Supabase
2. Fetch from localStorage
3. Merge and sort
4. Present unified view

#### 2. **useHabits** - Habit Tracking

**State Structure**:
- Array of habit objects
- Check-ins stored within each habit
- No separate loading/error states

**Operations**:
- `addHabit`: Creates habit and initial event
- `toggleCheckin`: Updates check-in array
- Immediate state updates
- Background Supabase sync

**Unique Aspects**:
- Dual table updates (habits + events)
- Array manipulation for check-ins
- Date-based toggle logic

#### 3. **useBlogPosts** - Blog Management

**State Structure**:
- Array of blog posts
- Missing some fields (status, body_md)
- No pagination support

**Inconsistencies**:
- Incomplete type definitions
- Doesn't fetch all fields
- Mismatch with blog utilities

**Operations**:
- CRUD operations
- Optimistic updates
- Manual refresh capability

#### 4. **useDashboardLayout** - Layout Persistence

**State Structure**:
- Array of block configurations
- Block type and unique ID
- Order preserved in array

**Dual Persistence**:
1. **Primary**: Supabase database
2. **Fallback**: localStorage
3. **Sync**: Save to both on changes

**Smart Features**:
- Load from Supabase first
- Fall back to localStorage
- Silent sync failures
- Timestamp-based IDs

#### 5. **useToast** - Notification System

**Unique Architecture**:
- Global state without Context
- External store pattern
- Reducer-based updates
- Event emitter pattern

**Implementation Details**:
- Custom event system
- Maximum 1 toast at a time
- Very long timeout (16+ minutes)
- No persistence needed

## UI State Management

### Component-Level State

**Common Patterns**:
```typescript
// Form state
const [title, setTitle] = useState('')
const [blocks, setBlocks] = useState<Block[]>([])

// UI state
const [isOpen, setIsOpen] = useState(false)
const [activeTab, setActiveTab] = useState(0)

// Derived state
const isValid = title.length > 0 && blocks.length > 0
```

### Form Management

**Current Approach**:
- Controlled components
- Local state for inputs
- No form library (react-hook-form unused)
- Manual validation

**Patterns**:
- Two-way binding
- OnChange handlers
- Submit prevention
- Reset after submission

## Data Fetching Patterns

### Initial Load Pattern

```typescript
useEffect(() => {
  async function loadData() {
    const { data } = await supabase
      .from('table')
      .select()
      .order('created_at', { ascending: false })
    
    if (data) setState(data)
  }
  
  loadData()
}, [])
```

**Characteristics**:
- Single fetch on mount
- No refetching
- No loading states
- Silent error handling

### Optimistic Update Pattern

```typescript
const updateItem = async (item: Type) => {
  // Update UI immediately
  setState(prev => prev.map(
    i => i.id === item.id ? item : i
  ))
  
  // Sync with database
  await supabase
    .from('table')
    .update(item)
    .eq('id', item.id)
}
```

**Benefits**:
- Instant feedback
- Better perceived performance
- Eventual consistency

**Risks**:
- No rollback on failure
- Potential sync issues
- Silent failures

## State Persistence

### Persistence Strategy

```
User Action
    ↓
Local State Update
    ↓
UI Update (Optimistic)
    ↓
Supabase Sync ──── Failure ──→ Silent
    ↓                            ↓
Success                    LocalStorage
```

### LocalStorage Usage

**Current Usage**:
1. Dashboard layouts
2. Public page layouts
3. Onboarding progress
4. Journal drafts

**Patterns**:
```typescript
// Save
localStorage.setItem(
  'lifesync.feature.data',
  JSON.stringify(data)
)

// Load
const stored = localStorage.getItem('lifesync.feature.data')
const data = stored ? JSON.parse(stored) : defaultValue
```

## Performance Considerations

### Current Optimizations

1. **Minimal Re-renders**: Local state updates
2. **Lazy Loading**: Suspense for async components
3. **Memoization**: Limited use, mostly natural
4. **Batch Updates**: React 18 automatic batching

### Performance Issues

1. **No Request Deduplication**: Multiple hooks = multiple requests
2. **Full Data Fetching**: No pagination
3. **Large State Objects**: Full replacement on updates
4. **Missing Caches**: Refetch on every mount

## Error Handling Philosophy

### Silent Failure Pattern

```typescript
try {
  const result = await operation()
  if (result.data) setState(result.data)
} catch (error) {
  // Silent failure - return default
  return []
}
```

**Rationale**:
- Prevents error states
- Maintains UI functionality
- Reduces user friction

**Drawbacks**:
- No user feedback
- Debugging difficulties
- Data loss potential

## State Management Anti-Patterns

### Identified Issues

1. **State Duplication**: Database data cached in state
2. **Missing Loading States**: No loading indicators
3. **No Error Boundaries**: Errors crash components
4. **Stale Closures**: Potential in effect callbacks
5. **Memory Leaks**: No cleanup in some effects

### Missing Patterns

1. **No Normalized State**: Duplicate data possible
2. **No Selectors**: Direct state access
3. **No Middleware**: No logging/debugging
4. **No Time Travel**: No undo/redo
5. **No State Machines**: Complex flows hard to track

## Testing State Management

### Current Testing Approach

```typescript
// Mock hooks
jest.mock('@/hooks/use-entries', () => ({
  useEntries: () => ({
    entries: mockEntries,
    createEntry: jest.fn()
  })
}))
```

**Patterns**:
- Mock at hook level
- Predictable test data
- No integration tests
- Limited state testing

## Best Practices

### Recommended Patterns

1. **Colocate State**: Keep state close to usage
2. **Lift When Needed**: Only lift when sharing required
3. **Derive Don't Sync**: Calculate from source data
4. **Single Source**: Database as truth
5. **Optimistic UI**: Update immediately

### Guidelines

1. **Custom Hooks**: Encapsulate complex logic
2. **Type Safety**: Full TypeScript coverage
3. **Error Handling**: At least log errors
4. **Performance**: Profile before optimizing
5. **Testing**: Test state logic separately

## Future Improvements

### Immediate Needs

1. **Loading States**: Add loading indicators
2. **Error States**: User-friendly error messages
3. **Request Caching**: Prevent duplicate fetches
4. **Pagination**: Handle large datasets
5. **Rollback**: Undo failed optimistic updates

### Architecture Evolution

1. **State Library**: Consider Zustand/Valtio for complex state
2. **Query Library**: TanStack Query for server state
3. **Form Library**: React Hook Form for complex forms
4. **Error Boundaries**: Graceful error handling
5. **State Machines**: XState for complex flows

### Advanced Patterns

1. **Normalized Cache**: Prevent data duplication
2. **Optimistic Rollback**: Revert failed updates
3. **Real-time Sync**: Supabase subscriptions
4. **Offline Queue**: Sync when reconnected
5. **State Persistence**: Broader localStorage use

## Migration Path

### To Improved Architecture

1. **Phase 1**: Add loading/error states
2. **Phase 2**: Implement request caching
3. **Phase 3**: Add state management library
4. **Phase 4**: Normalize state shape
5. **Phase 5**: Full real-time sync

### Minimal Disruption

- Start with new features
- Gradually refactor existing
- Maintain backward compatibility
- Test thoroughly
- Document changes

## Conclusion

LifeSync's state management demonstrates a pragmatic "just enough" approach. While this simplicity aids development velocity and understanding, the application is reaching a complexity level where more sophisticated patterns would provide better user experience, developer experience, and application reliability. The current architecture provides a solid foundation for gradual enhancement without requiring a complete rewrite.