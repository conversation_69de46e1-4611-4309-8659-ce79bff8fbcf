"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { ChevronLeft, Save } from "lucide-react";
import Link from "next/link";
import { addPost, BlogPost } from "@/lib/blog";
import { Block } from "@/lib/types";


export default function NewBlogPostPage() {
  const router = useRouter();
  const [title, setTitle] = useState("");
  const [blocks, setBlocks] = useState<Block[]>([
    { text: "", visibility: "public" },
  ]);
  const [body, setBody] = useState("");
  const [status, setStatus] = useState("draft");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const post: BlogPost = {
      id: Date.now().toString(),
      title,
      blocks,
      body_md: body,
      status,
    };
    await addPost(post);
    router.push("/blog");
  };

  const updateBlock = (index: number, field: keyof Block, value: string) => {
    setBlocks((prev) => {
      const copy = [...prev];
      copy[index] = { ...copy[index], [field]: value };
      return copy;
    });
  };

  const addBlockField = () => {
    setBlocks((prev) => [...prev, { text: "", visibility: "public" }]);
  };

  const removeBlock = (idx: number) => {
    setBlocks((prev) => prev.filter((_, i) => i !== idx));
  };

  return (
    <div className="container mx-auto py-2">
      <Link href="/blog" className="inline-flex items-center text-sm text-primary hover:underline mb-4">
        <ChevronLeft className="mr-1 h-4 w-4" />
        Back to Blog
      </Link>
      <Card className="w-full shadow-xl">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle className="text-2xl font-headline">New Blog Post</CardTitle>
            <CardDescription>Share your thoughts with the world.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="post-title" className="text-base">Title</Label>
              <Input
                id="post-title"
                placeholder="Post title"
                className="mt-1 text-base"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="post-body" className="text-base">Body</Label>
              <Textarea
                id="post-body"
                placeholder="Write your post here..."
                className="min-h-[200px] mt-1 text-base"
                value={body}
                onChange={(e) => setBody(e.target.value)}
              />
              <Button
                type="button"
                variant="secondary"
                className="mt-2"
                onClick={async () => {
                  const res = await fetch(
                    `/api/blog-ideas?topic=${encodeURIComponent(title)}`,
                  );
                  if (res.ok) {
                    const data = await res.json();
                    setBody(prev =>
                      prev ? `${prev}\n${data.ideas}` : data.ideas,
                    );
                  }
                }}
              >
                AI Assist
              </Button>
            </div>
            <div className="space-y-4">
              <Label className="text-base">Blocks</Label>
              {blocks.map((block, i) => (
                <div key={i} className="space-y-2 border p-2 rounded">
                  <Textarea
                    placeholder="Write your post here..."
                    className="min-h-[120px] text-base"
                    value={block.text}
                    onChange={(e) => updateBlock(i, 'text', e.target.value)}
                  />
                  <div className="flex items-center gap-2">
                    <Select
                      value={block.visibility}
                      onValueChange={(v) => updateBlock(i, 'visibility', v)}
                    >
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="Visibility" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="public">Public</SelectItem>
                        <SelectItem value="private">Private</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button type="button" variant="outline" size="sm" onClick={() => removeBlock(i)}>
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
              <Button type="button" variant="secondary" size="sm" onClick={addBlockField}>
                Add Block
              </Button>
            </div>
            <div>
              <Label className="text-base mb-1 block">Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="public">Public</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button variant="outline" asChild>
              <Link href="/blog">Cancel</Link>
            </Button>
            <Button type="submit">
              <Save className="mr-2 h-4 w-4" />
              Save Post
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

