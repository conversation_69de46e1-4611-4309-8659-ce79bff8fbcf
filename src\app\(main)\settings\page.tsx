
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Palette, FileDown, Link as LinkIcon, Users, Bell, ShieldCheck, CreditCard } from "lucide-react"; 
import Image from "next/image";

export default function SettingsPage() {
  return (
    <div className="container mx-auto py-2">
      <h1 className="text-3xl font-bold mb-8 font-headline text-foreground">Settings</h1>
      <Tabs defaultValue="account" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 mb-6">
          <TabsTrigger value="account">
            <Users className="mr-2 h-4 w-4" /> Account
          </TabsTrigger>
          <TabsTrigger value="privacy">
            <ShieldCheck className="mr-2 h-4 w-4" /> Privacy
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="mr-2 h-4 w-4" /> Notifications
          </TabsTrigger>
          <TabsTrigger value="appearance">
            <Palette className="mr-2 h-4 w-4" /> Appearance
          </TabsTrigger>
           <TabsTrigger value="subscription">
            <CreditCard className="mr-2 h-4 w-4" /> Subscription
          </TabsTrigger>
          <TabsTrigger value="export">
            <FileDown className="mr-2 h-4 w-4" /> Data Export
          </TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Account Settings</CardTitle>
              <CardDescription>Manage your account details and preferences.</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x150.png" alt="Account settings placeholder" width={300} height={150} className="rounded-md mb-4" data-ai-hint="profile user" />
              <p>Manage email, password, username, and account deletion here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="privacy">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Privacy Settings</CardTitle>
              <CardDescription>Control your data visibility and sharing options.</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x150.png" alt="Privacy settings placeholder" width={300} height={150} className="rounded-md mb-4" data-ai-hint="security lock" />
              <p>Configure default visibility, data sharing, AI processing permissions, etc.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Notification Settings</CardTitle>
              <CardDescription>Choose what updates you receive and how.</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x150.png" alt="Notifications placeholder" width={300} height={150} className="rounded-md mb-4" data-ai-hint="bell alert" />
              <p>Manage push, email, reminder, and activity notifications.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Appearance & Theme</CardTitle>
              <CardDescription>Customize the look and feel of LifeSync AI.</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x150.png" alt="Theme settings placeholder" width={300} height={150} className="rounded-md mb-4" data-ai-hint="color palette" />
              <p>Theme, layout density, dark/light mode options will be here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscription">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Subscription & Billing</CardTitle>
              <CardDescription>Manage your LifeSync AI premium subscription.</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x150.png" alt="Subscription placeholder" width={300} height={150} className="rounded-md mb-4" data-ai-hint="payment card" />
              <p>Subscription management and billing information will be here.</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="export">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Data Export</CardTitle>
              <CardDescription>Export your LifeSync AI data.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Export Your Data</h3>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline">
                    <FileDown className="mr-2 h-4 w-4" /> Export as Markdown
                  </Button>
                  <Button variant="outline">
                    <FileDown className="mr-2 h-4 w-4" /> Export as PDF
                  </Button>
                   <Button variant="outline">
                    <FileDown className="mr-2 h-4 w-4" /> Export as CSV
                  </Button>
                </div>
              </div>
              <Image src="https://placehold.co/300x150.png" alt="Data export placeholder" width={300} height={150} className="rounded-md my-4 mx-auto" data-ai-hint="files folders" />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
