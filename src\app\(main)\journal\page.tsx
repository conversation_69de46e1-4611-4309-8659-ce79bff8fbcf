
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { format } from "date-fns";
import { useEntries } from "@/hooks/use-entries";
import { useSearch } from "@/hooks/use-search";

export default function JournalPage() {
  const { entries } = useEntries();
  
  const { query, setQuery, filteredItems, hasQuery, resultsCount } = useSearch({
    items: entries,
    searchFields: ['title', 'text'],
    filterFn: (entry, searchQuery) => {
      // Search in title
      if (entry.title?.toLowerCase().includes(searchQuery)) {
        return true
      }
      
      // Search in entry text content
      if (entry.text?.toLowerCase().includes(searchQuery)) {
        return true
      }
      
      // Search in mood
      if (entry.mood?.toLowerCase().includes(searchQuery)) {
        return true
      }
      
      return false
    }
  });

  return (
    <div className="container mx-auto py-2">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
        <h1 className="text-3xl font-bold font-headline text-foreground">My Journal</h1>
        <div className="flex gap-2 w-full sm:w-auto">
          <div className="relative flex-grow sm:flex-grow-0">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search entries..."
              className="pl-8 sm:w-[200px] md:w-[300px]"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
            />
            {hasQuery && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1 h-7 w-7 p-0"
                onClick={() => setQuery('')}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
          <Button asChild>
            <Link href="/journal/new">
              <PlusCircle className="mr-2 h-4 w-4" /> New Entry
            </Link>
          </Button>
        </div>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="font-headline">
            Journal Entries
            {hasQuery && (
              <span className="text-sm font-normal text-muted-foreground ml-2">
                ({resultsCount} result{resultsCount === 1 ? '' : 's'})
              </span>
            )}
          </CardTitle>
          <CardDescription>
            {hasQuery 
              ? `Showing results for "${query}"`
              : "All your thoughts, reflections, and moments, organized in one place."
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="min-h-[300px]">
          {filteredItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
              {hasQuery ? (
                <>
                  <p className="text-lg">No entries found for "{query}"</p>
                  <p className="text-sm mt-2">Try adjusting your search terms.</p>
                </>
              ) : entries.length === 0 ? (
                <>
                  <p className="text-lg">No journal entries yet.</p>
                  <p className="text-sm mt-2">Click "New Entry" to start writing.</p>
                </>
              ) : (
                <>
                  <p className="text-lg">No matching entries found.</p>
                  <p className="text-sm mt-2">Try a different search term.</p>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredItems.map((entry) => (
                <div key={entry.id} className="border-b pb-2 flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">
                      {entry.title || "Untitled Entry"}
                    </h3>
                    <div className="flex items-center gap-2">
                      <p className="text-sm text-muted-foreground">
                        {format(new Date(entry.createdAt), "PP p")}
                        {entry.mood ? ` – ${entry.mood}` : ""}
                      </p>
                      {entry.isLocal && (
                        <Badge variant="secondary">Local</Badge>
                      )}
                    </div>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/journal/edit/${entry.id}`}>Edit</Link>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
