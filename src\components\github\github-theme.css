/* GitHub-style theme for LifeSync */
:root {
  /* GitHub Colors */
  --gh-black: #24292e;
  --gh-gray-dark: #586069;
  --gh-gray: #6a737d;
  --gh-gray-light: #d1d5da;
  --gh-gray-lighter: #e1e4e8;
  --gh-gray-lightest: #f6f8fa;
  --gh-white: #ffffff;
  
  /* Contribution Graph Greens */
  --gh-green-0: #ebedf0;
  --gh-green-1: #9be9a8;
  --gh-green-2: #40c463;
  --gh-green-3: #30a14e;
  --gh-green-4: #216e39;
  
  /* Status Colors */
  --gh-green: #28a745;
  --gh-red: #d73a49;
  --gh-blue: #0366d6;
  --gh-yellow: #ffd33d;
  --gh-purple: #6f42c1;
  --gh-orange: #f66a0a;
  
  /* GitHub Font Stack */
  --gh-font-stack: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  --gh-font-mono: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
}

/* GitHub-style utility classes */
.gh-text-primary { color: var(--gh-black); }
.gh-text-secondary { color: var(--gh-gray-dark); }
.gh-text-muted { color: var(--gh-gray); }
.gh-text-link { color: var(--gh-blue); }
.gh-text-success { color: var(--gh-green); }
.gh-text-danger { color: var(--gh-red); }

.gh-bg-primary { background-color: var(--gh-white); }
.gh-bg-secondary { background-color: var(--gh-gray-lightest); }
.gh-bg-tertiary { background-color: var(--gh-gray-lighter); }

.gh-border { border-color: var(--gh-gray-lighter); }
.gh-border-dark { border-color: var(--gh-gray-light); }

/* GitHub-style components */
.gh-repo-card {
  background: var(--gh-white);
  border: 1px solid var(--gh-gray-lighter);
  border-radius: 6px;
  padding: 16px;
  transition: all 0.2s ease;
}

.gh-repo-card:hover {
  border-color: var(--gh-gray-light);
  box-shadow: 0 1px 3px rgba(27,31,35,0.12), 0 0 0 1px rgba(27,31,35,0.04);
}

.gh-tab {
  padding: 8px 16px;
  border-bottom: 2px solid transparent;
  color: var(--gh-gray-dark);
  transition: all 0.2s ease;
}

.gh-tab:hover {
  color: var(--gh-black);
  border-bottom-color: var(--gh-gray-light);
}

.gh-tab.active {
  color: var(--gh-black);
  font-weight: 600;
  border-bottom-color: var(--gh-orange);
}

.gh-contribution-square {
  width: 11px;
  height: 11px;
  border-radius: 2px;
  outline: 1px solid rgba(27,31,35,0.06);
  outline-offset: -1px;
}

.gh-label {
  display: inline-block;
  padding: 0 7px;
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  border-radius: 2em;
  border: 1px solid transparent;
}

.gh-label-high {
  background-color: #f9c513;
  color: #24292e;
}

.gh-label-medium {
  background-color: #fb8500;
  color: #fff;
}

.gh-label-low {
  background-color: #0e8a16;
  color: #fff;
}