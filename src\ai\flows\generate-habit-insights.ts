'use server';

/**
 * @fileOverview A flow to analyze recent habit progress and provide short insights.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';

const GenerateHabitInsightsInputSchema = z.object({
  habits: z
    .array(z.string())
    .describe('Descriptions of habit progress, one per habit'),
});
export type GenerateHabitInsightsInput = z.infer<typeof GenerateHabitInsightsInputSchema>;

const GenerateHabitInsightsOutputSchema = z
  .string()
  .describe('Brief insight or suggestion about the user\'s habits.');
export type GenerateHabitInsightsOutput = z.infer<typeof GenerateHabitInsightsOutputSchema>;

export async function generateHabitInsights(
  input: GenerateHabitInsightsInput,
): Promise<GenerateHabitInsightsOutput> {
  return generateHabitInsightsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateHabitInsightsPrompt',
  input: { schema: GenerateHabitInsightsInputSchema },
  output: { schema: GenerateHabitInsightsOutputSchema },
  prompt:
    `You are a habit coach. Using the provided habit progress, give a concise insight or suggestion (max three sentences) to help the user stay consistent.`,
});

const generateHabitInsightsFlow = ai.defineFlow(
  {
    name: 'generateHabitInsightsFlow',
    inputSchema: GenerateHabitInsightsInputSchema,
    outputSchema: GenerateHabitInsightsOutputSchema,
  },
  async ({ habits }) => {
    const { output } = await prompt({ habits: habits.join('\n') });
    return output!;
  },
);
