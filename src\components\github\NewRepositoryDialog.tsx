"use client"

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { GitBranch, Lock, Globe } from 'lucide-react'
import { cn } from '@/lib/utils'

interface NewRepositoryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: NewHabitData) => void
}

export interface NewHabitData {
  name: string
  description?: string
  frequency: number
  category: string
  visibility: 'public' | 'private'
  enableReminders: boolean
  reminderTime?: string
  template?: string
}

const habitTemplates = [
  { id: 'custom', name: 'Start from scratch', description: 'Create a custom habit' },
  { id: 'exercise', name: 'Exercise Template', description: 'Daily physical activity tracking' },
  { id: 'meditation', name: 'Meditation Template', description: 'Mindfulness and meditation practice' },
  { id: 'reading', name: 'Reading Template', description: 'Track your reading progress' },
  { id: 'hydration', name: 'Hydration Template', description: 'Track daily water intake' },
  { id: 'sleep', name: 'Sleep Template', description: 'Monitor sleep patterns' },
]

const categories = [
  { value: 'health', label: 'Health & Fitness', emoji: '🏃' },
  { value: 'mental', label: 'Mental Wellness', emoji: '🧘' },
  { value: 'learning', label: 'Learning & Growth', emoji: '📚' },
  { value: 'work', label: 'Work & Career', emoji: '💼' },
  { value: 'personal', label: 'Personal Life', emoji: '🌟' },
  { value: 'social', label: 'Social & Relationships', emoji: '👥' },
]

export function NewRepositoryDialog({ open, onOpenChange, onSubmit }: NewRepositoryDialogProps) {
  const [formData, setFormData] = useState<Partial<NewHabitData>>({
    name: '',
    description: '',
    frequency: 3,
    category: 'personal',
    visibility: 'public',
    enableReminders: false,
    template: 'custom',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name) return

    onSubmit({
      name: formData.name,
      description: formData.description,
      frequency: formData.frequency || 3,
      category: formData.category || 'personal',
      visibility: formData.visibility || 'public',
      enableReminders: formData.enableReminders || false,
      reminderTime: formData.reminderTime,
      template: formData.template,
    })

    // Reset form
    setFormData({
      name: '',
      description: '',
      frequency: 3,
      category: 'personal',
      visibility: 'public',
      enableReminders: false,
      template: 'custom',
    })
  }

  const selectedCategory = categories.find(c => c.value === formData.category)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create a new repository</DialogTitle>
            <DialogDescription>
              A repository contains all project files, including the revision history.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 mt-6">
            {/* Template Selection */}
            <div>
              <Label>Repository template</Label>
              <Select
                value={formData.template}
                onValueChange={(value) => setFormData({ ...formData, template: value })}
              >
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {habitTemplates.map(template => (
                    <SelectItem key={template.id} value={template.id}>
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-xs text-muted-foreground">{template.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground mt-1">
                Start with a template or create from scratch
              </p>
            </div>

            <Separator />

            {/* Owner and Repository Name */}
            <div>
              <Label htmlFor="habit-name">Repository name *</Label>
              <div className="flex items-center gap-2 mt-2">
                <Input
                  id="owner"
                  value="You"
                  disabled
                  className="w-32"
                />
                <span className="text-muted-foreground">/</span>
                <Input
                  id="habit-name"
                  placeholder="my-awesome-habit"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                  className="flex-1"
                />
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Great repository names are short and memorable.
              </p>
            </div>

            {/* Description */}
            <div>
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                placeholder="What's this habit about?"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="mt-2 min-h-[80px]"
              />
            </div>

            <Separator />

            {/* Category and Frequency */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData({ ...formData, category: value })}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue>
                      {selectedCategory && (
                        <span className="flex items-center gap-2">
                          <span>{selectedCategory.emoji}</span>
                          <span>{selectedCategory.label}</span>
                        </span>
                      )}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category.value} value={category.value}>
                        <span className="flex items-center gap-2">
                          <span>{category.emoji}</span>
                          <span>{category.label}</span>
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="frequency">Frequency (times per week)</Label>
                <Input
                  id="frequency"
                  type="number"
                  min="1"
                  max="7"
                  value={formData.frequency}
                  onChange={(e) => setFormData({ ...formData, frequency: parseInt(e.target.value) })}
                  className="mt-2"
                />
              </div>
            </div>

            {/* Visibility */}
            <div>
              <Label>Visibility</Label>
              <RadioGroup
                value={formData.visibility}
                onValueChange={(value: 'public' | 'private') => setFormData({ ...formData, visibility: value })}
                className="mt-2 space-y-3"
              >
                <div className="flex items-start space-x-3">
                  <RadioGroupItem value="public" id="public" className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor="public" className="flex items-center gap-2 font-normal cursor-pointer">
                      <Globe className="w-4 h-4" />
                      <span className="font-medium">Public</span>
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Anyone can see this repository. You choose who can commit.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <RadioGroupItem value="private" id="private" className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor="private" className="flex items-center gap-2 font-normal cursor-pointer">
                      <Lock className="w-4 h-4" />
                      <span className="font-medium">Private</span>
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      You choose who can see and commit to this repository.
                    </p>
                  </div>
                </div>
              </RadioGroup>
            </div>

            <Separator />

            {/* Initialize repository */}
            <div>
              <Label>Initialize this repository with:</Label>
              <div className="mt-3 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Add daily reminders</p>
                    <p className="text-xs text-muted-foreground">
                      Get notified to complete your habit
                    </p>
                  </div>
                  <Switch
                    checked={formData.enableReminders}
                    onCheckedChange={(checked) => setFormData({ ...formData, enableReminders: checked })}
                  />
                </div>

                {formData.enableReminders && (
                  <div>
                    <Label htmlFor="reminder-time">Reminder time</Label>
                    <Input
                      id="reminder-time"
                      type="time"
                      value={formData.reminderTime || '09:00'}
                      onChange={(e) => setFormData({ ...formData, reminderTime: e.target.value })}
                      className="mt-2 w-32"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" className="bg-[#2ea44f] hover:bg-[#2c974b]">
              <GitBranch className="w-4 h-4 mr-2" />
              Create repository
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}