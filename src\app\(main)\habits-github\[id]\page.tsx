"use client"

import React, { useState, useMemo } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  GitCommit, Code, AlertCircle, Target, Users, Settings,
  ArrowLeft, Star, GitFork, Lock, Globe
} from 'lucide-react'
import { useHabits } from '@/hooks/use-habits'
import { useAuth } from '@/components/auth-provider'
import { ContributionGraph } from '@/components/github/ContributionGraph'
import { ActivityFeed, Activity } from '@/components/github/ActivityFeed'
import { MilestonesCard, Milestone } from '@/components/github/MilestonesCard'
import { useHabitSocial } from '@/hooks/use-habit-social'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { toast } from '@/hooks/use-toast'
import '@/components/github/github-theme.css'

export default function HabitDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const { habits, loading, toggleCheckin } = useHabits()
  const [milestones, setMilestones] = useState<Milestone[]>([])
  
  const habitId = params.id as string
  const habit = habits.find(h => h.id === habitId)
  const { socialData, starHabit, forkHabit } = useHabitSocial(habitId)
  
  // Generate contribution data for this specific habit
  const contributionData = useMemo(() => {
    if (!habit) return {}
    const data: Record<string, number> = {}
    habit.checkins?.forEach((date: string) => {
      data[date] = 1
    })
    return data
  }, [habit])
  
  // Generate activity feed for this habit
  const activities = useMemo(() => {
    if (!habit) return []
    const activities: Activity[] = []
    
    // Recent commits
    habit.checkins?.slice(-10).forEach((date: string) => {
      activities.push({
        id: `commit-${date}`,
        type: 'commit',
        user: { name: 'You' },
        habitName: habit.name,
        message: '',
        timestamp: new Date(date),
        metadata: { count: 1 }
      })
    })
    
    // Add milestone achievements
    if (habit.stats.streak.current === 7) {
      activities.push({
        id: 'achievement-7',
        type: 'achievement',
        user: { name: 'You' },
        habitName: habit.name,
        message: '',
        timestamp: new Date(),
        metadata: { streak: 7 }
      })
    }
    
    return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }, [habit])
  
  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-muted-foreground">Loading repository...</div>
        </div>
      </div>
    )
  }
  
  if (!habit) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-2">Repository not found</h2>
          <p className="text-muted-foreground mb-4">The habit you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/habits-github')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to repositories
          </Button>
        </div>
      </div>
    )
  }
  
  const handleCreateMilestone = async (milestone: Omit<Milestone, 'id' | 'status' | 'currentValue'>) => {
    const newMilestone: Milestone = {
      ...milestone,
      id: Date.now().toString(),
      currentValue: 0,
      status: 'active'
    }
    setMilestones([...milestones, newMilestone])
  }
  
  const handleUpdateMilestone = async (id: string, updates: Partial<Milestone>) => {
    setMilestones(milestones.map(m => m.id === id ? { ...m, ...updates } : m))
  }
  
  const handleDeleteMilestone = async (id: string) => {
    setMilestones(milestones.filter(m => m.id !== id))
  }
  
  return (
    <div className="container mx-auto py-4" style={{ fontFamily: 'var(--gh-font-stack)' }}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/habits-github')}
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back
          </Button>
          <span>/</span>
          <span>{user?.email?.split('@')[0] || 'user'}</span>
          <span>/</span>
          <span className="font-medium text-foreground">{habit.name}</span>
        </div>
        
        <div className="flex items-start justify-between">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-2xl font-semibold">{habit.name}</h1>
              <Badge 
                variant="outline" 
                className="text-xs px-2 py-0 h-5 font-normal"
              >
                <Globe className="w-3 h-3 mr-1" />
                Public
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Track {habit.name} {habit.frequency}x per week
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => starHabit(habitId)}
            >
              <Star className={cn("w-4 h-4 mr-2", socialData[habitId]?.isStarred && "fill-current")} />
              {socialData[habitId]?.isStarred ? 'Starred' : 'Star'}
              <Badge variant="secondary" className="ml-2">
                {socialData[habitId]?.starCount || 0}
              </Badge>
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={async () => {
                const newHabitId = await forkHabit(habitId)
                if (newHabitId) {
                  toast({
                    title: 'Success!',
                    description: 'Habit forked to your repositories'
                  })
                  router.push('/habits-github')
                }
              }}
            >
              <GitFork className="w-4 h-4 mr-2" />
              Fork
              {socialData[habitId]?.forkCount ? (
                <Badge variant="secondary" className="ml-2">
                  {socialData[habitId].forkCount}
                </Badge>
              ) : null}
            </Button>
          </div>
        </div>
      </div>
      
      {/* Stats Bar */}
      <div className="flex items-center gap-6 py-3 border-b mb-6 text-sm">
        <div className="flex items-center gap-2">
          <GitCommit className="w-4 h-4" />
          <span className="font-medium">{habit.stats.totalCompletions}</span>
          <span className="text-muted-foreground">commits</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">🔥 {habit.stats.streak.current}</span>
          <span className="text-muted-foreground">day streak</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">{habit.stats.weeklyCompletionRate}%</span>
          <span className="text-muted-foreground">weekly completion</span>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Tabs defaultValue="code" className="w-full">
            <TabsList className="bg-transparent border-b rounded-none h-auto p-0 w-full justify-start">
              <TabsTrigger 
                value="code" 
                className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
              >
                <Code className="w-4 h-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger 
                value="commits"
                className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
              >
                <GitCommit className="w-4 h-4 mr-2" />
                Commits
              </TabsTrigger>
              <TabsTrigger 
                value="issues"
                className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
              >
                <AlertCircle className="w-4 h-4 mr-2" />
                Issues
              </TabsTrigger>
              <TabsTrigger 
                value="settings"
                className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="code" className="mt-6">
              <Card className="p-6">
                <h3 className="text-sm font-medium mb-4">Contribution activity</h3>
                <ContributionGraph data={contributionData} weeks={52} />
              </Card>
            </TabsContent>
            
            <TabsContent value="commits" className="mt-6">
              <Card className="p-6">
                <h3 className="text-sm font-medium mb-4">Recent commits</h3>
                <ActivityFeed activities={activities} />
              </Card>
            </TabsContent>
            
            <TabsContent value="issues" className="mt-6">
              <Card className="p-6">
                <div className="text-center py-8 text-muted-foreground">
                  <AlertCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No open issues</p>
                  <p className="text-sm mt-2">
                    Track tasks and goals related to this habit
                  </p>
                </div>
              </Card>
            </TabsContent>
            
            <TabsContent value="settings" className="mt-6">
              <Card className="p-6">
                <h3 className="text-lg font-medium mb-4">Habit Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Frequency</label>
                    <p className="text-sm text-muted-foreground">
                      {habit.frequency} times per week
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Created</label>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(habit.created_at), 'PPP')}
                    </p>
                  </div>
                  <div className="pt-4 border-t">
                    <Button variant="destructive" size="sm">
                      Archive this repository
                    </Button>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        {/* Sidebar */}
        <div className="space-y-6">
          {/* About */}
          <Card className="p-4">
            <h3 className="font-medium mb-3">About</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Track {habit.name} {habit.frequency}x per week
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                <span>{habit.frequency}x weekly goal</span>
              </div>
              <div className="flex items-center gap-2">
                <GitCommit className="w-4 h-4" />
                <span>{habit.stats.totalCompletions} total commits</span>
              </div>
              <div className="flex items-center gap-2">
                <span>🔥</span>
                <span>{habit.stats.streak.current} day streak</span>
              </div>
            </div>
          </Card>
          
          {/* Milestones */}
          <MilestonesCard
            habitId={habit.id}
            habitName={habit.name}
            milestones={milestones}
            currentStreak={habit.stats.streak.current}
            totalCompletions={habit.stats.totalCompletions}
            onCreateMilestone={handleCreateMilestone}
            onUpdateMilestone={handleUpdateMilestone}
            onDeleteMilestone={handleDeleteMilestone}
          />
          
          {/* Quick Actions */}
          <Card className="p-4">
            <h3 className="font-medium mb-3">Quick Actions</h3>
            <div className="space-y-2">
              <Button
                className="w-full justify-start"
                variant={habit.isCompletedToday ? "secondary" : "default"}
                onClick={() => toggleCheckin(habit.id)}
              >
                <GitCommit className="w-4 h-4 mr-2" />
                {habit.isCompletedToday ? 'Completed Today' : 'Mark Complete'}
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}