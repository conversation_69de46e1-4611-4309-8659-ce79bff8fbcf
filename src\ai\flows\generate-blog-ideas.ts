'use server';

/**
 * @fileOverview A flow to generate short blog post ideas based on a topic.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';

const GenerateBlogIdeasInputSchema = z.object({
  topic: z.string().describe('The topic or title for the blog post'),
});
export type GenerateBlogIdeasInput = z.infer<typeof GenerateBlogIdeasInputSchema>;

const GenerateBlogIdeasOutputSchema = z
  .string()
  .describe('A short list of blog post ideas.');
export type GenerateBlogIdeasOutput = z.infer<typeof GenerateBlogIdeasOutputSchema>;

export async function generateBlogIdeas(
  input: GenerateBlogIdeasInput,
): Promise<GenerateBlogIdeasOutput> {
  return generateBlogIdeasFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateBlogIdeasPrompt',
  input: { schema: GenerateBlogIdeasInputSchema },
  output: { schema: GenerateBlogIdeasOutputSchema },
  prompt: `You are a helpful assistant. Suggest three engaging blog post ideas about "{topic}". Present them as a numbered list.`,
});

const generateBlogIdeasFlow = ai.defineFlow(
  {
    name: 'generateBlogIdeasFlow',
    inputSchema: GenerateBlogIdeasInputSchema,
    outputSchema: GenerateBlogIdeasOutputSchema,
  },
  async ({ topic }) => {
    const { output } = await prompt({ topic });
    return output!;
  },
);
