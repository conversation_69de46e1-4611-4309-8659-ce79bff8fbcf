-- Add user_id and week_start to weekly_summaries table
alter table public.weekly_summaries 
add column if not exists user_id uuid references auth.users(id),
add column if not exists week_start timestamp with time zone;

-- Update RLS policy for weekly summaries
drop policy if exists "Enable read access for all users" on weekly_summaries;

create policy "Users can view own summaries"
  on weekly_summaries for select
  using (auth.uid() = user_id);

create policy "System can insert summaries"
  on weekly_summaries for insert
  with check (true); -- Allow system/cron to insert

-- Create index for better performance
create index if not exists idx_weekly_summaries_user_week 
  on weekly_summaries(user_id, week_start desc);