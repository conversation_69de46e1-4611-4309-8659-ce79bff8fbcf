import { render, screen } from '@testing-library/react'
import BlogPage from '@/app/(main)/blog/page'
import { useBlogPosts } from '@/hooks/use-blog-posts'

jest.mock('@/hooks/use-blog-posts', () => ({
  useBlogPosts: jest.fn(),
}))

const mockedUseBlogPosts = useBlogPosts as jest.MockedFunction<typeof useBlogPosts>

test('lists blog posts', () => {
  mockedUseBlogPosts.mockReturnValue({ posts: [
    { id: '1', title: 'First Post', body: 'Hi', status: 'draft' },
    { id: '2', title: 'Second Post', body: 'Hello', status: 'public' },
  ], createPost: jest.fn(), updatePost: jest.fn(), refresh: jest.fn() })
  render(<BlogPage />)
  expect(screen.getByText('First Post')).toBeInTheDocument()
  expect(screen.getByText('Second Post')).toBeInTheDocument()
})

