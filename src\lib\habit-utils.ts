import { parseISO, differenceInDays, isYesterday, isToday, startOfDay } from 'date-fns';

export interface HabitStreak {
  current: number;
  longest: number;
  lastCompletionDate: Date | null;
}

export interface HabitStats {
  totalCompletions: number;
  currentWeekCompletions: number;
  streak: HabitStreak;
  completionRate: number; // percentage for current week
}

/**
 * Calculate habit streaks from check-in dates
 */
export function calculateHabitStreak(checkins: string[]): HabitStreak {
  if (!checkins || checkins.length === 0) {
    return {
      current: 0,
      longest: 0,
      lastCompletionDate: null
    };
  }

  // Parse and sort dates in descending order (most recent first)
  const dates = checkins
    .map(dateStr => {
      try {
        return startOfDay(parseISO(dateStr));
      } catch {
        return null;
      }
    })
    .filter((date): date is Date => date !== null)
    .sort((a, b) => b.getTime() - a.getTime());

  if (dates.length === 0) {
    return {
      current: 0,
      longest: 0,
      lastCompletionDate: null
    };
  }

  const lastCompletionDate = dates[0];
  const today = startOfDay(new Date());

  // Calculate current streak
  let currentStreak = 0;
  let streakDate = today;

  // Check if we should start counting from today or yesterday
  if (isToday(lastCompletionDate)) {
    currentStreak = 1;
    streakDate = lastCompletionDate;
  } else if (isYesterday(lastCompletionDate)) {
    currentStreak = 1;
    streakDate = lastCompletionDate;
  } else {
    // Last completion was more than a day ago, so current streak is 0
    return {
      current: 0,
      longest: calculateLongestStreak(dates),
      lastCompletionDate
    };
  }

  // Count consecutive days backwards from the streak date
  for (let i = 1; i < dates.length; i++) {
    const prevDate = dates[i];
    const expectedDate = new Date(streakDate);
    expectedDate.setDate(expectedDate.getDate() - 1);

    if (differenceInDays(streakDate, prevDate) === 1) {
      currentStreak++;
      streakDate = prevDate;
    } else {
      break;
    }
  }

  return {
    current: currentStreak,
    longest: Math.max(currentStreak, calculateLongestStreak(dates)),
    lastCompletionDate
  };
}

/**
 * Calculate the longest streak in a series of dates
 */
function calculateLongestStreak(sortedDates: Date[]): number {
  if (sortedDates.length === 0) return 0;
  if (sortedDates.length === 1) return 1;

  let longestStreak = 1;
  let currentStreak = 1;

  for (let i = 1; i < sortedDates.length; i++) {
    const daysDiff = differenceInDays(sortedDates[i - 1], sortedDates[i]);
    
    if (daysDiff === 1) {
      currentStreak++;
      longestStreak = Math.max(longestStreak, currentStreak);
    } else {
      currentStreak = 1;
    }
  }

  return longestStreak;
}

/**
 * Calculate comprehensive habit statistics
 */
export function calculateHabitStats(
  checkins: string[],
  frequency: number,
  weekStart: Date
): HabitStats {
  const streak = calculateHabitStreak(checkins);
  
  // Calculate this week's completions
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekEnd.getDate() + 7);
  
  const currentWeekCompletions = checkins.filter(dateStr => {
    try {
      const date = parseISO(dateStr);
      return date >= weekStart && date < weekEnd;
    } catch {
      return false;
    }
  }).length;

  return {
    totalCompletions: checkins.length,
    currentWeekCompletions,
    streak,
    completionRate: Math.min((currentWeekCompletions / frequency) * 100, 100)
  };
}

/**
 * Check if a habit should be completed today based on frequency
 */
export function shouldCompleteToday(
  checkins: string[],
  frequency: number,
  weekStart: Date
): boolean {
  const stats = calculateHabitStats(checkins, frequency, weekStart);
  return stats.currentWeekCompletions < frequency;
}

/**
 * Get the next milestone for a habit streak
 */
export function getNextMilestone(currentStreak: number): number {
  const milestones = [7, 14, 30, 60, 90, 180, 365];
  return milestones.find(milestone => milestone > currentStreak) || currentStreak + 30;
}

/**
 * Format streak display text
 */
export function formatStreakText(streak: HabitStreak): string {
  if (streak.current === 0) {
    return streak.longest > 0 
      ? `Best streak: ${streak.longest} day${streak.longest === 1 ? '' : 's'}`
      : 'No streak yet';
  }
  
  return `${streak.current} day${streak.current === 1 ? '' : 's'} streak`;
}