'use client'

import { useState } from 'react'
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  KeyboardSensor,
} from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import type { DashboardBlock } from '@/hooks/use-dashboard-layout'
import { useDashboardLayout } from '@/hooks/use-dashboard-layout'
import BlockContainer from './block-container'
import { Button } from '@/components/ui/button'

interface DashboardBlocksProps {
  blocksMap: Record<string, React.ReactNode>
  layoutHook?: () => {
    blocks: DashboardBlock[]
    addBlock: (type: string) => void
    removeBlock: (id: string) => void
    moveBlock: (oldIndex: number, newIndex: number) => void
  }
}

export default function DashboardBlocks({ blocksMap, layoutHook = useDashboardLayout }: DashboardBlocksProps) {
  const { blocks, addBlock, removeBlock, moveBlock } = layoutHook()
  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor))
  const [newType, setNewType] = useState(Object.keys(blocksMap)[0] || '')

  const handleDragEnd = (event: any) => {
    const { active, over } = event
    if (active.id !== over?.id) {
      const oldIndex = blocks.findIndex(b => b.id === active.id)
      const newIndex = blocks.findIndex(b => b.id === over?.id)
      if (oldIndex >= 0 && newIndex >= 0) {
        moveBlock(oldIndex, newIndex)
      }
    }
  }

  return (
    <div>
      <div className="flex items-center gap-2 mb-4">
        <select
          value={newType}
          onChange={e => setNewType(e.target.value)}
          className="border rounded px-2 py-1 bg-background text-foreground"
        >
          {Object.keys(blocksMap).map(key => (
            <option key={key} value={key}>
              {key}
            </option>
          ))}
        </select>
        <Button onClick={() => addBlock(newType)}>Add Block</Button>
      </div>
      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext items={blocks.map(b => b.id)} strategy={verticalListSortingStrategy}>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {blocks.map(block => (
              <BlockContainer key={block.id} id={block.id} onRemove={removeBlock}>
                {blocksMap[block.type] ?? <div>Unknown block</div>}
              </BlockContainer>
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  )
}
