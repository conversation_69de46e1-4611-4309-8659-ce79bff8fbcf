# LifeSync AI

LifeSync AI is an AI‑powered personal life management app built with Next.js and Genkit. It provides journaling, habit tracking and other productivity tools.

To get started, take a look at src/app/(main)/page.tsx.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Prerequisites

- Node.js 20 or later

## Local setup

Install dependencies and start the development servers:

```bash
npm install
npm run dev         # Next.js app
npm run genkit:dev  # Genkit dev server
```

Open `src/app/(main)/page.tsx` to start exploring the UI.

## Folder structure

- `src/ai` – Genkit configuration and AI flows
- `src/app` – Next.js routes and pages
- `src/components` – UI components
- `src/hooks` – Custom hooks
- `src/lib` – Utility types and helpers
- `docs` – Project docs and blueprints

To get started:

1. Copy `.env.example` to `.env` and fill in the required keys.
2. Add your Supabase credentials (`SUPABASE_URL`, `SUPABASE_ANON_KEY`, and
   optionally `SUPABASE_SERVICE_ROLE_KEY`).
3. Take a look at `src/app/(main)/page.tsx`.

## Running tests

Unit tests are written with Jest and React Testing Library, located under the
`tests` folder (e.g. `tests/pages/habits`). Run them with:

```bash
npm test
```

