"use client";

import { useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { CardContent, CardFooter } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { addJournalEntry, JournalEntry } from "@/lib/journal-store";

interface ReflectionFormProps {
  onSaved?: (entry: JournalEntry) => void;
}

export default function ReflectionForm({ onSaved }: ReflectionFormProps) {
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();
  const [text, setText] = useState("");

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    startTransition(() => {
      const entry = addJournalEntry(text);
      setText("");
      toast({ title: "Reflection saved" });
      onSaved?.(entry);
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <CardContent>
        <Textarea
          name="reflection"
          placeholder="Write your thoughts here..."
          className="min-h-[120px] text-base"
          aria-label="Morning reflection input"
          value={text}
          onChange={(e) => setText(e.target.value)}
        />
      </CardContent>
      <CardFooter>
        <Button type="submit" className="ml-auto" disabled={isPending}>
          {isPending ? "Saving..." : "Save Reflection"}
        </Button>
      </CardFooter>
    </form>
  );
}
