'use client';

import { useEffect, useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

export default function HabitInsightsCard() {
  const [insights, setInsights] = useState('');

  useEffect(() => {
    fetch('/api/habit-insights')
      .then(res => res.json())
      .then(data => setInsights(data.insights))
      .catch(() => setInsights(''));
  }, []);

  if (!insights) return null;

  return (
    <Card className="mb-6 shadow-lg">
      <CardHeader>
        <CardTitle className="font-headline">AI Insights</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="whitespace-pre-wrap text-muted-foreground">{insights}</p>
      </CardContent>
    </Card>
  );
}
