
"use client";

import type { LogEntry, LogEntryStatus, LogEntryCategory } from "@/lib/types";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ListChecks, CheckSquare, Calendar, Edit3, Circle, Zap, GripVertical, Tag, MinusCircle, ArrowRightCircle } from "lucide-react";

// Mock initial entries
const initialLogEntries: LogEntry[] = [
  { id: "1", content: "Team meeting at 10 AM", category: "Work Event", status: "scheduled", createdAt: new Date() },
  { id: "2", content: "Finish project proposal", category: "Task", status: "open", createdAt: new Date() },
  { id: "3", content: "Brainstorm new app ideas", category: "Ideas", status: "open", createdAt: new Date() },
];

// Using a generic Tag icon for user-defined categories for now
const CategoryIcon = Tag;

const LogEntryStatusIcons: Record<LogEntryStatus, React.ElementType> = {
  open: Circle,
  done: CheckSquare,
  migrated: ArrowRightCircle,
  scheduled: Calendar,
  cancelled: MinusCircle,
};

export function RapidLog() {
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const [newEntryContent, setNewEntryContent] = useState("");
  const [newEntryCategory, setNewEntryCategory] = useState<LogEntryCategory>("Task"); // Default category

  // Avoid hydration errors with initial data
  useEffect(() => {
    setLogEntries(initialLogEntries);
  }, []);


  const addLogEntry = () => {
    if (!newEntryContent.trim()) return;
    const newEntry: LogEntry = {
      id: String(Date.now()), // Simple ID generation for demo
      content: newEntryContent,
      category: newEntryCategory.trim() || "Uncategorized", // Default if empty
      status: "open",
      createdAt: new Date(),
    };
    setLogEntries((prevEntries) => [newEntry, ...prevEntries]);
    setNewEntryContent("");
    setNewEntryCategory("Task"); // Reset to default or could be last used
  };

  const toggleLogEntryStatus = (id: string) => {
    setLogEntries(
      logEntries.map((entry) =>
        entry.id === id
          ? { ...entry, status: entry.status === "open" ? "done" : "open" }
          : entry
      )
    );
  };

  return (
    <Card className="w-full shadow-lg mt-6">
      <CardHeader>
        <CardTitle className="font-headline flex items-center gap-2">
          <ListChecks className="h-6 w-6 text-primary" />
          Rapid Log
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-2 mb-4">
          <Input
            value={newEntryContent}
            onChange={(e) => setNewEntryContent(e.target.value)}
            placeholder="Log anything: a task, an idea, a workout..."
            className="flex-grow"
            aria-label="New log entry content"
          />
          <Input
            value={newEntryCategory}
            onChange={(e) => setNewEntryCategory(e.target.value)}
            placeholder="Category (e.g., Work, Fitness)"
            className="sm:w-[180px]"
            aria-label="New log entry category"
          />
          <Button onClick={addLogEntry} aria-label="Add log entry" className="mt-2 sm:mt-0">Add Entry</Button>
        </div>
        {logEntries.length === 0 ? (
          <p className="text-muted-foreground text-center py-4">No entries yet. Add your first item!</p>
        ) : (
          <ul className="space-y-3">
            {logEntries.map((entry) => {
              const StatusIcon = LogEntryStatusIcons[entry.status] || Circle; // Fallback to Circle icon
              return (
                <li
                  key={entry.id}
                  className="flex items-center gap-3 p-3 bg-secondary/30 rounded-md"
                >
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => toggleLogEntryStatus(entry.id)}
                    aria-label={`Toggle status for ${entry.content}`}
                    className="shrink-0"
                  >
                    <StatusIcon className={`h-5 w-5 ${entry.status === "done" ? "text-green-500" : "text-muted-foreground"}`} />
                  </Button>
                  <CategoryIcon className="h-5 w-5 text-primary shrink-0" title={entry.category} />
                  <div className="flex-grow">
                    <span className={`${entry.status === "done" ? "line-through text-muted-foreground" : ""}`}>
                      {entry.content}
                    </span>
                    <span className="block text-xs text-muted-foreground">{entry.category}</span>
                  </div>
                  <span className="text-xs text-muted-foreground shrink-0">
                    {entry.createdAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </li>
              );
            })}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}
