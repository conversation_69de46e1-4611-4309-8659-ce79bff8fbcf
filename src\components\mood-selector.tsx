import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export const MOOD_OPTIONS = [
  { value: "happy", label: "Happy 😊" },
  { value: "neutral", label: "Neutral 😐" },
  { value: "sad", label: "Sad 😔" },
];

interface MoodSelectorProps {
  value: string;
  onChange: (value: string) => void;
}

export function MoodSelector({ value, onChange }: MoodSelectorProps) {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger id="mood-selector" className="w-[140px]">
        <SelectValue placeholder="Select mood" />
      </SelectTrigger>
      <SelectContent>
        {MOOD_OPTIONS.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
