import { renderHook, waitFor } from '@testing-library/react'
import { useEntries } from '@/hooks/use-entries'
import * as store from '@/lib/journal-store'

beforeEach(() => {
  jest.resetAllMocks()
  localStorage.clear()
})

test('merges local and remote entries', async () => {
  jest.spyOn(store, 'getAllEntries').mockReturnValue([
    { id: 1, text: 'Local note', createdAt: '2024-01-02T00:00:00.000Z' },
  ])

  global.fetch = jest.fn().mockResolvedValue({
    json: () =>
      Promise.resolve({
        data: {
          entries: [
            {
              id: 'g1',
              title: 'Remote',
              content: 'Remote content',
              created_at: '2024-01-01T00:00:00.000Z',
            },
          ],
        },
      }),
  }) as jest.Mock

  const { result } = renderHook(() => useEntries())

  await waitFor(() => expect(result.current.entries.length).toBe(2))

  const hasLocal = result.current.entries.some((e) => e.isLocal)
  expect(hasLocal).toBe(true)
})

