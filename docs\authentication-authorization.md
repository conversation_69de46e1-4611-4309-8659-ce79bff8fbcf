# Authentication and Authorization Flow Documentation

## Overview

LifeSync AI uses Su<PERSON><PERSON> for authentication and authorization, implementing a client-side authentication pattern with Row Level Security (RLS) for data protection. The application uses Next.js 14 with the App Router.

## Authentication Setup

### 1. Supabase Configuration

The application uses two Supabase clients:

#### Client-Side (Browser) Client
- **File**: `/src/lib/supabase.ts`
- Uses `createBrowserClient` from `@supabase/ssr`
- Configured with public environment variables:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `NEXT_PUBLIC_SUPABASE_ANON_KEY`

```typescript
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string

export const supabase = createBrowserClient(supabaseUrl, supabase<PERSON><PERSON><PERSON>ey)
```

#### Server-Side Client
- **File**: `/src/lib/supabase-server.ts`
- Uses standard `createClient` from `@supabase/supabase-js`
- Can use either service role key or anon key
- Used for server-side operations (API routes, server components)

```typescript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = (process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY) as string;

export const supabase = createClient(supabaseUrl, supabaseKey);
```

### 2. Session Management

#### AuthProvider Component
- **File**: `/src/components/auth-provider.tsx`
- Provides authentication context throughout the application
- Manages session state and auth state changes
- Exposes `useAuth()` hook for accessing auth state

Key features:
- Fetches initial session on mount
- Subscribes to auth state changes
- Provides `user`, `session`, and `signOut` function
- Automatically updates when auth state changes

```typescript
interface AuthContextType {
  user: User | null;
  session: Session | null;
  signOut: () => Promise<void>;
}
```

### 3. Authentication Flow

#### Registration Process
- **File**: `/src/app/(auth)/register/page.tsx`
- Collects email, username, and password
- Uses `supabase.auth.signUp()` with user metadata
- Stores username in localStorage for quick access
- Redirects to home page on success

```typescript
const { data, error } = await supabase.auth.signUp({
  email,
  password,
  options: { data: { username } },
});
```

#### Login Process
- **File**: `/src/app/(auth)/login/page.tsx`
- Uses `supabase.auth.signInWithPassword()`
- Retrieves username from user metadata
- Stores in localStorage for quick access
- Redirects to home page on success

#### Logout
- Handled by `signOut()` function from `useAuth()` hook
- Calls `supabase.auth.signOut()`

## Client vs Server-Side Auth

### Client-Side Authentication
- All authentication in the current implementation is client-side
- Uses the `AuthProvider` component wrapped around the entire app
- Session is managed in React state with automatic updates
- No middleware or server-side session validation

### Current Implementation Limitations
1. **No Protected Routes**: Routes are not protected at the routing level
2. **Client-Side Only**: All auth checks happen in the browser
3. **No Server-Side Session Validation**: API routes don't validate sessions
4. **No Middleware**: No Next.js middleware for route protection

## Protected Routes Implementation

### Current State
The application uses a layout-based structure but doesn't enforce authentication:

1. **Auth Layout** (`/src/app/(auth)/layout.tsx`): 
   - For login/register pages
   - Simple centered layout

2. **Main Layout** (`/src/app/(main)/layout.tsx`):
   - For authenticated app pages
   - Includes sidebar navigation
   - **Does not check authentication**

3. **Onboarding Layout** (`/src/app/(onboarding)/layout.tsx`):
   - For new user onboarding
   - Tracks progress through steps
   - **Does not verify user is authenticated**

### Recommended Improvements

To properly protect routes, implement:

1. **Middleware** for route protection:
```typescript
// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'

export async function middleware(req) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })
  const { data: { session } } = await supabase.auth.getSession()

  // Protect routes
  if (!session && req.nextUrl.pathname.startsWith('/(main)')) {
    return NextResponse.redirect(new URL('/login', req.url))
  }

  return res
}
```

2. **Server-Side Session Validation** in API routes
3. **Protected Page Components** that check auth state

## Security Policies and RLS

### Row Level Security (RLS)
- **File**: `/supabase/policies.sql`
- All tables have RLS enabled
- Policies ensure users can only access their own data

#### Policy Pattern
Each table follows the same pattern:
- **SELECT**: `auth.uid() = user_id`
- **INSERT**: `auth.uid() = user_id`
- **UPDATE**: `auth.uid() = user_id`

#### Tables with RLS:
1. **entries**: Journal entries
2. **habits**: User habits
3. **moods**: Mood tracking
4. **blog_posts**: Blog posts
5. **followers**: Following relationships (special rules)
6. **weekly_summaries**: Read-only for all authenticated users
7. **dashboard_layouts**: Dashboard customization
8. **habit_events**: Habit tracking events

#### Special Cases:
- **followers**: Can view if you're the follower OR the followed
- **weekly_summaries**: All authenticated users can read (no user_id check)

### Data Model Security
All user-specific tables reference `auth.users` with foreign keys:
```sql
user_id uuid references auth.users not null
```

## Onboarding Flow

The onboarding process consists of 5 steps:
1. **Welcome** (`/welcome`): Introduction to the platform
2. **Privacy** (`/privacy`): Privacy policy acceptance
3. **AI Personality** (`/ai-personality`): Configure AI assistant
4. **First Entry** (`/first-entry`): Create first journal entry
5. **Tutorial** (`/tutorial`): Platform tutorial

Progress is tracked in localStorage but **not verified server-side**.

## Security Recommendations

### Critical Issues to Address:

1. **Implement Route Protection**
   - Add middleware for auth checking
   - Redirect unauthenticated users to login
   - Protect API routes

2. **Server-Side Session Validation**
   - Validate sessions in API routes
   - Use server-side Supabase client properly
   - Check auth before data operations

3. **Secure API Endpoints**
   - Current API routes (e.g., `/api/chat`, `/api/weekly-summary`) have no auth checks
   - Add session validation to all API routes

4. **Environment Variables**
   - Consider using service role key only for specific admin operations
   - Keep it server-side only

5. **Session Management**
   - Implement proper session refresh
   - Handle expired sessions gracefully
   - Add loading states during auth checks

### Example Secure API Route:
```typescript
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET() {
  const supabase = createRouteHandlerClient({ cookies })
  
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  
  // Proceed with authenticated request
}
```

## Summary

The current implementation provides basic authentication functionality but lacks proper route protection and server-side validation. While RLS policies protect data at the database level, the application would benefit from additional security layers at the application level to ensure a robust authentication and authorization system.