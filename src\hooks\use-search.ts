"use client"

import { useState, useMemo } from 'react'

interface SearchableItem {
  id: string
  [key: string]: any
}

interface UseSearchOptions<T> {
  items: T[]
  searchFields: (keyof T)[]
  filterFn?: (item: T, query: string) => boolean
}

export function useSearch<T extends SearchableItem>({
  items,
  searchFields,
  filterFn
}: UseSearchOptions<T>) {
  const [query, setQuery] = useState('')

  const filteredItems = useMemo(() => {
    if (!query.trim()) return items

    const normalizedQuery = query.toLowerCase().trim()

    return items.filter(item => {
      // Use custom filter function if provided
      if (filterFn) {
        return filterFn(item, normalizedQuery)
      }

      // Default search implementation
      return searchFields.some(field => {
        const value = item[field]
        if (typeof value === 'string') {
          return value.toLowerCase().includes(normalizedQuery)
        }
        if (Array.isArray(value)) {
          return value.some(v => 
            typeof v === 'string' && v.toLowerCase().includes(normalizedQuery)
          )
        }
        return false
      })
    })
  }, [items, query, searchFields, filterFn])

  return {
    query,
    setQuery,
    filteredItems,
    hasQuery: query.trim().length > 0,
    resultsCount: filteredItems.length
  }
}