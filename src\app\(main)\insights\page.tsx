'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function InsightsPage() {
  const [insights, setInsights] = useState<string>('');

  useEffect(() => {
    fetch('/api/weekly-summary')
      .then(res => res.json())
      .then(data => setInsights(data.summary))
      .catch(() => setInsights(''));
  }, []);

  return (
    <div className="container mx-auto py-2">
      <h1 className="text-3xl font-bold font-headline text-foreground mb-8">
        Habit Insights
      </h1>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="font-headline">This Week</CardTitle>
        </CardHeader>
        <CardContent>
          {insights ? (
            <p className="whitespace-pre-wrap text-muted-foreground">{insights}</p>
          ) : (
            <p className="text-muted-foreground">No insights available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
