
export type LogEntryCategory = string; // Users can define their own categories
export type LogEntryStatus = 'open' | 'done' | 'migrated' | 'scheduled' | 'cancelled' | string; // Allow for custom statuses in future

export interface LogEntry {
  id: string;
  content: string;
  category: LogEntryCategory;
  status: LogEntryStatus;
  createdAt: Date;
  // Potentially add a field for user-defined custom data per entry in the future
  // data?: Record<string, any>;
}

export interface Habit {
  id: string
  name: string
  frequency: number
  checkins: string[]
}

export interface Block {
  text: string
  visibility: 'private' | 'public'
}

export type BlogPostStatus = 'draft' | 'public' | string

export interface BlogPost {
  id: string
  title: string
  blocks: Block[]
  body_md?: string
  status: BlogPostStatus
}
