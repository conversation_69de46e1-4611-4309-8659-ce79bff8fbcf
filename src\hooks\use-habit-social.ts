"use client"

import { useState, useEffect, useCallback } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { useAuth } from '@/components/auth-provider'
import { toast } from '@/hooks/use-toast'

export interface HabitSocialData {
  habitId: string
  starCount: number
  forkCount: number
  isStarred: boolean
}

export function useHabitSocial(habitId?: string) {
  const { user } = useAuth()
  const [socialData, setSocialData] = useState<Record<string, HabitSocialData>>({}) 
  const [loading, setLoading] = useState(false)
  const supabase = createClientComponentClient()

  const fetchSocialData = useCallback(async (habitIds: string[]) => {
    if (!user || habitIds.length === 0) return

    try {
      setLoading(true)
      
      // Fetch star counts
      const { data: starData, error: starError } = await supabase
        .from('habit_stars')
        .select('habit_id')
        .in('habit_id', habitIds)
      
      if (starError) throw starError
      
      // Fetch fork counts
      const { data: forkData, error: forkError } = await supabase
        .from('habit_forks')
        .select('original_habit_id')
        .in('original_habit_id', habitIds)
      
      if (forkError) throw forkError
      
      // Fetch user's stars
      const { data: userStars, error: userStarsError } = await supabase
        .from('habit_stars')
        .select('habit_id')
        .eq('user_id', user.id)
        .in('habit_id', habitIds)
      
      if (userStarsError) throw userStarsError
      
      // Process data
      const newSocialData: Record<string, HabitSocialData> = {}
      
      habitIds.forEach(id => {
        newSocialData[id] = {
          habitId: id,
          starCount: starData?.filter(s => s.habit_id === id).length || 0,
          forkCount: forkData?.filter(f => f.original_habit_id === id).length || 0,
          isStarred: userStars?.some(s => s.habit_id === id) || false
        }
      })
      
      setSocialData(prev => ({ ...prev, ...newSocialData }))
    } catch (error) {
      console.error('Error fetching social data:', error)
    } finally {
      setLoading(false)
    }
  }, [user, supabase])

  useEffect(() => {
    if (habitId) {
      fetchSocialData([habitId])
    }
  }, [habitId, fetchSocialData])

  const starHabit = async (habitId: string) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'Please sign in to star habits',
        variant: 'destructive'
      })
      return
    }

    try {
      const isCurrentlyStarred = socialData[habitId]?.isStarred || false
      
      if (isCurrentlyStarred) {
        // Unstar
        const { error } = await supabase
          .from('habit_stars')
          .delete()
          .eq('habit_id', habitId)
          .eq('user_id', user.id)
        
        if (error) throw error
        
        setSocialData(prev => ({
          ...prev,
          [habitId]: {
            ...prev[habitId],
            starCount: Math.max(0, (prev[habitId]?.starCount || 0) - 1),
            isStarred: false
          }
        }))
        
        toast({
          title: 'Unstarred',
          description: 'Habit removed from your stars'
        })
      } else {
        // Star
        const { error } = await supabase
          .from('habit_stars')
          .insert({
            habit_id: habitId,
            user_id: user.id
          })
        
        if (error) throw error
        
        setSocialData(prev => ({
          ...prev,
          [habitId]: {
            ...prev[habitId],
            starCount: (prev[habitId]?.starCount || 0) + 1,
            isStarred: true
          }
        }))
        
        toast({
          title: 'Starred!',
          description: 'Habit added to your stars'
        })
      }
    } catch (error) {
      console.error('Error starring habit:', error)
      toast({
        title: 'Error',
        description: 'Failed to update star status',
        variant: 'destructive'
      })
    }
  }

  const forkHabit = async (habitId: string) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'Please sign in to fork habits',
        variant: 'destructive'
      })
      return
    }

    try {
      // Call the fork_habit function
      const { data, error } = await supabase
        .rpc('fork_habit', { original_id: habitId })
      
      if (error) throw error
      
      setSocialData(prev => ({
        ...prev,
        [habitId]: {
          ...prev[habitId],
          forkCount: (prev[habitId]?.forkCount || 0) + 1
        }
      }))
      
      toast({
        title: 'Forked!',
        description: 'Habit copied to your repositories',
      })
      
      return data // Return the new habit ID
    } catch (error) {
      console.error('Error forking habit:', error)
      toast({
        title: 'Error',
        description: 'Failed to fork habit',
        variant: 'destructive'
      })
    }
  }

  return {
    socialData,
    loading,
    starHabit,
    forkHabit,
    fetchSocialData
  }
}