"use client"

import React, { useMemo } from 'react'
import { format, startOfWeek, endOfWeek, eachDayOfInterval, subWeeks, isToday } from 'date-fns'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'

interface ContributionDay {
  date: Date
  count: number
  level: 0 | 1 | 2 | 3 | 4
}

interface ContributionGraphProps {
  data: Record<string, number> // date string (YYYY-MM-DD) -> count
  weeks?: number
  className?: string
}

export function ContributionGraph({ data, weeks = 52, className }: ContributionGraphProps) {
  const contributions = useMemo(() => {
    const endDate = endOfWeek(new Date(), { weekStartsOn: 0 })
    const startDate = startOfWeek(subWeeks(endDate, weeks - 1), { weekStartsOn: 0 })
    
    const days = eachDayOfInterval({ start: startDate, end: endDate })
    
    return days.map(date => {
      const dateStr = format(date, 'yyyy-MM-dd')
      const count = data[dateStr] || 0
      
      // Calculate level based on count
      let level: ContributionDay['level'] = 0
      if (count > 0) level = 1
      if (count >= 3) level = 2
      if (count >= 5) level = 3
      if (count >= 8) level = 4
      
      return { date, count, level }
    })
  }, [data, weeks])
  
  // Group contributions by week
  const contributionsByWeek = useMemo(() => {
    const weeks: ContributionDay[][] = []
    for (let i = 0; i < contributions.length; i += 7) {
      weeks.push(contributions.slice(i, i + 7))
    }
    return weeks
  }, [contributions])
  
  // Get month labels
  const monthLabels = useMemo(() => {
    const labels: { month: string; col: number }[] = []
    let lastMonth = ''
    
    contributionsByWeek.forEach((week, weekIndex) => {
      const firstDayOfWeek = week[0]
      if (firstDayOfWeek) {
        const month = format(firstDayOfWeek.date, 'MMM')
        if (month !== lastMonth) {
          labels.push({ month, col: weekIndex })
          lastMonth = month
        }
      }
    })
    
    return labels
  }, [contributionsByWeek])
  
  const dayLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
  
  const getSquareColor = (level: ContributionDay['level']) => {
    const colors = {
      0: '#ebedf0',
      1: '#9be9a8',
      2: '#40c463',
      3: '#30a14e',
      4: '#216e39'
    }
    return colors[level]
  }
  
  return (
    <TooltipProvider>
      <div className={cn("inline-block", className)}>
        <div className="mb-2 flex gap-1 text-xs text-muted-foreground">
          <div className="w-8" /> {/* Spacer for day labels */}
          {monthLabels.map(({ month, col }) => (
            <div
              key={`${month}-${col}`}
              className="absolute"
              style={{ left: `${40 + col * 13}px` }}
            >
              {month}
            </div>
          ))}
        </div>
        
        <div className="flex gap-1">
          {/* Day labels */}
          <div className="flex flex-col gap-1 text-xs text-muted-foreground pr-2">
            <div className="h-3" /> {/* Spacer */}
            {dayLabels.filter((_, i) => i % 2 === 1).map(day => (
              <div key={day} className="h-[22px] flex items-center">
                {day}
              </div>
            ))}
          </div>
          
          {/* Contribution squares */}
          <div className="flex gap-1">
            {contributionsByWeek.map((week, weekIndex) => (
              <div key={weekIndex} className="flex flex-col gap-1">
                {week.map((day, dayIndex) => (
                  <Tooltip key={`${weekIndex}-${dayIndex}`}>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          "w-[11px] h-[11px] rounded-sm cursor-pointer transition-all",
                          "outline outline-1 outline-offset-[-1px] outline-gray-200",
                          isToday(day.date) && "outline-2 outline-gray-600"
                        )}
                        style={{ backgroundColor: getSquareColor(day.level) }}
                      />
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-sm">
                        <div className="font-semibold">
                          {day.count} contribution{day.count !== 1 ? 's' : ''}
                        </div>
                        <div className="text-muted-foreground">
                          {format(day.date, 'MMM d, yyyy')}
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                ))}
              </div>
            ))}
          </div>
        </div>
        
        <div className="mt-3 flex items-center gap-1 text-xs text-muted-foreground">
          <span>Less</span>
          {[0, 1, 2, 3, 4].map(level => (
            <div
              key={level}
              className="w-[11px] h-[11px] rounded-sm outline outline-1 outline-offset-[-1px] outline-gray-200"
              style={{ backgroundColor: getSquareColor(level as ContributionDay['level']) }}
            />
          ))}
          <span>More</span>
        </div>
      </div>
    </TooltipProvider>
  )
}