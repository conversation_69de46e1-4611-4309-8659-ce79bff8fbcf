import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Create a Supabase client configured to use cookies
  const response = NextResponse.next()
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Get session
  const { data: { session }, error } = await supabase.auth.getSession()

  // Define protected routes
  const protectedRoutes = ['/journal', '/habits', '/blog', '/ai-chat', '/insights', '/recap', '/collections', '/settings']
  const authRoutes = ['/login', '/register']
  const onboardingRoutes = ['/welcome', '/privacy', '/ai-personality', '/tutorial', '/first-entry']
  
  const path = request.nextUrl.pathname

  // Check if the path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => path.startsWith(route))
  const isAuthRoute = authRoutes.some(route => path.startsWith(route))
  const isOnboardingRoute = onboardingRoutes.some(route => path.startsWith(route))
  const isApiRoute = path.startsWith('/api')

  // Redirect logic
  if (!session && isProtectedRoute) {
    // No session and trying to access protected route - redirect to login
    const redirectUrl = new URL('/login', request.url)
    redirectUrl.searchParams.set('redirect', path)
    return NextResponse.redirect(redirectUrl)
  }

  if (session && isAuthRoute) {
    // Has session and trying to access auth route - redirect to home
    return NextResponse.redirect(new URL('/', request.url))
  }

  // API route protection
  if (isApiRoute && !path.startsWith('/api/auth')) {
    if (!session) {
      return new NextResponse(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401,
          headers: { 'content-type': 'application/json' }
        }
      )
    }
  }

  // Check onboarding completion
  if (session && !isOnboardingRoute && !isAuthRoute && !isApiRoute) {
    // Check if user has completed onboarding
    const { data: profile } = await supabase
      .from('profiles')
      .select('onboarding_completed')
      .eq('id', session.user.id)
      .single()

    if (profile && !profile.onboarding_completed && !path.startsWith('/welcome')) {
      return NextResponse.redirect(new URL('/welcome', request.url))
    }
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}