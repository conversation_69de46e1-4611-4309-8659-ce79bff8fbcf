-- Row level security policies
alter table entries enable row level security;
create policy "select own entries" on entries
  for select using (auth.uid() = user_id);
create policy "insert own entries" on entries
  for insert with check (auth.uid() = user_id);
create policy "update own entries" on entries
  for update using (auth.uid() = user_id);

alter table habits enable row level security;
create policy "select own habits" on habits
  for select using (auth.uid() = user_id);
create policy "insert own habits" on habits
  for insert with check (auth.uid() = user_id);
create policy "update own habits" on habits
  for update using (auth.uid() = user_id);

alter table moods enable row level security;
create policy "select own moods" on moods
  for select using (auth.uid() = user_id);
create policy "insert own moods" on moods
  for insert with check (auth.uid() = user_id);
create policy "update own moods" on moods
  for update using (auth.uid() = user_id);

alter table blog_posts enable row level security;
create policy "select own posts" on blog_posts
  for select using (auth.uid() = user_id);
create policy "insert own posts" on blog_posts
  for insert with check (auth.uid() = user_id);
create policy "update own posts" on blog_posts
  for update using (auth.uid() = user_id);

alter table followers enable row level security;
create policy "select followers" on followers
  for select using (auth.uid() = follower_id or auth.uid() = followed_id);
create policy "insert followers" on followers
  for insert with check (auth.uid() = follower_id);
create policy "delete followers" on followers
  for delete using (auth.uid() = follower_id or auth.uid() = followed_id);
  
alter table weekly_summaries enable row level security;
create policy "read summaries" on weekly_summaries
  for select using (true);

alter table dashboard_layouts enable row level security;
create policy "select own dashboard layouts" on dashboard_layouts
  for select using (auth.uid() = user_id);
create policy "insert own dashboard layouts" on dashboard_layouts
  for insert with check (auth.uid() = user_id);
create policy "update own dashboard layouts" on dashboard_layouts
  for update using (auth.uid() = user_id);

alter table habit_events enable row level security;
create policy "select own habit events" on habit_events
  for select using (auth.uid() = user_id);
create policy "insert own habit events" on habit_events
  for insert with check (auth.uid() = user_id);
