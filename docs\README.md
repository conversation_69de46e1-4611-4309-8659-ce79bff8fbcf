# LifeSync Technical Documentation

Welcome to the comprehensive technical documentation for LifeSync AI. This documentation provides in-depth insights into the architecture, modules, workflows, and technical implementation of the application.

## Documentation Structure

### 📋 Overview
- [Architecture Overview](./architecture-overview.md) - High-level system design and key architectural decisions

### 📦 Module Documentation
Detailed documentation for each major feature module:
- [Journal Module](./modules/journal-module.md) - Core journaling system with mood tracking and reflections
- [Habits Module](./modules/habits-module.md) - Habit tracking, progress monitoring, and AI insights
- [Blog Module](./modules/blog-module.md) - Publishing platform with privacy controls and social features
- [Dashboard Module](./modules/dashboard-module.md) - Customizable dashboard with drag-and-drop blocks

### 🔧 Technical Deep Dives
In-depth technical documentation:
- [AI Integration](./technical/ai-integration.md) - Genkit setup, flows, and prompt engineering
- [Database Design](./technical/database-design.md) - Schema architecture, relationships, and security
- [State Management](./technical/state-management.md) - React hooks, data flow, and persistence patterns

### 🔄 Workflows
Step-by-step guides for common tasks:
- [Feature Development](./workflows/feature-development.md) - How to add new features to LifeSync
- [Data Flow](./workflows/data-flow.md) - Understanding how data moves through the application

### 🔒 Security
- [Security Documentation](./security.md) - Current implementation, vulnerabilities, and recommendations

## Quick Links

### For Developers
1. Start with the [Architecture Overview](./architecture-overview.md)
2. Read the [Feature Development Workflow](./workflows/feature-development.md)
3. Understand [Data Flow](./workflows/data-flow.md) patterns
4. Review [Security](./security.md) considerations

### For Understanding Specific Features
- **Journaling**: [Journal Module](./modules/journal-module.md)
- **Habit Tracking**: [Habits Module](./modules/habits-module.md)
- **Content Publishing**: [Blog Module](./modules/blog-module.md)
- **User Dashboard**: [Dashboard Module](./modules/dashboard-module.md)

### For Technical Implementation
- **AI Features**: [AI Integration](./technical/ai-integration.md)
- **Database**: [Database Design](./technical/database-design.md)
- **Frontend State**: [State Management](./technical/state-management.md)

## Key Technologies

- **Frontend**: Next.js 15, React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL), Edge Functions
- **AI**: Google Genkit with Gemini 2.0 Flash
- **UI Components**: shadcn/ui design system
- **Testing**: Jest, React Testing Library

## Architecture Highlights

### Design Principles
1. **User Privacy First**: Granular content visibility controls
2. **AI Enhancement**: Intelligent features without overwhelming users
3. **Offline Capability**: LocalStorage fallbacks for core features
4. **Mobile Responsive**: Fully functional on all devices
5. **Type Safety**: TypeScript throughout the application

### Module Organization
```
LifeSync
├── Journal (Core reflection and writing)
├── Habits (Behavior tracking and insights)
├── Blog (Public content sharing)
├── Dashboard (Personalized command center)
└── AI Services (Intelligent assistance)
```

## Current Status

### ✅ Implemented
- Core journaling with block-based privacy
- Habit tracking with weekly goals
- AI-powered prompts and insights
- Blog publishing with draft/public states
- Customizable dashboard
- Basic authentication flow
- Database schema with RLS

### 🚧 In Progress
- Search functionality
- Habit streak tracking
- Public profile enhancements
- Rich text editing

### 📋 Planned
- Real-time collaboration
- Advanced analytics
- Mobile applications
- Notification system
- Content templates

## Security Notes

⚠️ **Important**: Review the [Security Documentation](./security.md) for critical vulnerabilities that need immediate attention, particularly around:
- Server-side route protection
- API endpoint authentication
- Input validation
- Rate limiting

## Contributing

When contributing to LifeSync:
1. Follow the patterns outlined in [Feature Development](./workflows/feature-development.md)
2. Ensure proper TypeScript types (avoid `any`)
3. Add tests for new features
4. Update relevant documentation
5. Consider security implications

## Getting Help

- Check existing documentation first
- Review similar features for patterns
- Test in development environment
- Consider mobile and offline scenarios

## Documentation Maintenance

This documentation should be updated when:
- Adding new features
- Changing architectural patterns
- Modifying database schema
- Updating security measures
- Discovering important workflows

---

*Last updated: [Current Date]*
*Documentation Version: 1.0*