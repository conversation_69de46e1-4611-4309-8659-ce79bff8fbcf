"use client"

import React from 'react'
import { Star, GitFork, Circle, Lock, Globe } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

export interface Repository {
  id: string
  name: string
  description?: string
  visibility: 'public' | 'private'
  stars: number
  forks: number
  commits: number
  language?: {
    name: string
    color: string
  }
  topics?: string[]
  lastCommit?: Date
  streak?: number
  collaborators?: number
  isStarred?: boolean
}

interface RepositoryCardProps {
  repository: Repository
  onStar?: () => void
  onFork?: () => void
  onClick?: () => void
  className?: string
}

export function RepositoryCard({ 
  repository, 
  onStar, 
  onFork, 
  onClick,
  className 
}: RepositoryCardProps) {
  return (
    <div 
      className={cn(
        "p-4 border rounded-md bg-background hover:border-muted-foreground/40 transition-all cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <h3 className="font-semibold text-[#0366d6] hover:underline">
            {repository.name}
          </h3>
          <Badge 
            variant="outline" 
            className="text-xs px-2 py-0 h-5 font-normal border-muted-foreground/40"
          >
            {repository.visibility === 'private' ? (
              <>
                <Lock className="w-3 h-3 mr-1" />
                Private
              </>
            ) : (
              <>
                <Globe className="w-3 h-3 mr-1" />
                Public
              </>
            )}
          </Badge>
        </div>
        
        {repository.streak && repository.streak > 0 && (
          <Badge variant="secondary" className="text-xs">
            🔥 {repository.streak} day streak
          </Badge>
        )}
      </div>
      
      {repository.description && (
        <p className="text-sm text-muted-foreground mb-3">
          {repository.description}
        </p>
      )}
      
      {repository.topics && repository.topics.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {repository.topics.map(topic => (
            <Badge 
              key={topic} 
              variant="secondary" 
              className="text-xs px-2 py-0 h-5 bg-[#f1f8ff] text-[#0366d6] hover:bg-[#ddeeff]"
            >
              {topic}
            </Badge>
          ))}
        </div>
      )}
      
      <div className="flex items-center gap-4 text-xs text-muted-foreground">
        {repository.language && (
          <div className="flex items-center gap-1">
            <Circle 
              className="w-3 h-3 fill-current" 
              style={{ color: repository.language.color }}
            />
            <span>{repository.language.name}</span>
          </div>
        )}
        
        <button
          className="flex items-center gap-1 hover:text-[#0366d6] transition-colors"
          onClick={(e) => {
            e.stopPropagation()
            onStar?.()
          }}
        >
          <Star className={cn("w-4 h-4", repository.isStarred && "fill-current")} />
          <span>{repository.stars}</span>
        </button>
        
        {repository.visibility === 'public' && (
          <button
            className="flex items-center gap-1 hover:text-[#0366d6] transition-colors"
            onClick={(e) => {
              e.stopPropagation()
              onFork?.()
            }}
          >
            <GitFork className="w-4 h-4" />
            <span>{repository.forks}</span>
          </button>
        )}
        
        {repository.collaborators && repository.collaborators > 0 && (
          <div className="flex items-center gap-1">
            <span>👥</span>
            <span>{repository.collaborators}</span>
          </div>
        )}
        
        {repository.lastCommit && (
          <span>
            Updated {formatDistanceToNow(repository.lastCommit, { addSuffix: true })}
          </span>
        )}
      </div>
    </div>
  )
}