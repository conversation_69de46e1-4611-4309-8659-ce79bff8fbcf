-- Accountability Partners Schema

-- Partners relationship table
create table if not exists public.accountability_partners (
  id uuid primary key default gen_random_uuid(),
  requester_id uuid references auth.users not null,
  partner_id uuid references auth.users not null,
  status text not null default 'pending' check (status in ('pending', 'accepted', 'declined', 'removed')),
  message text,
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now(),
  constraint unique_partner_pair unique(requester_id, partner_id),
  constraint no_self_partner check (requester_id != partner_id)
);

-- Shared habits between partners
create table if not exists public.shared_habits (
  id uuid primary key default gen_random_uuid(),
  habit_id uuid references public.habits not null,
  partnership_id uuid references public.accountability_partners not null,
  created_at timestamp with time zone default now(),
  constraint unique_shared_habit unique(habit_id, partnership_id)
);

-- Partner activities/notifications
create table if not exists public.partner_activities (
  id uuid primary key default gen_random_uuid(),
  partnership_id uuid references public.accountability_partners not null,
  actor_id uuid references auth.users not null,
  activity_type text not null check (activity_type in ('habit_completed', 'streak_milestone', 'habit_created', 'encouragement')),
  habit_id uuid references public.habits,
  message text,
  metadata jsonb,
  created_at timestamp with time zone default now()
);

-- Indexes for performance
create index idx_accountability_partners_requester on public.accountability_partners(requester_id);
create index idx_accountability_partners_partner on public.accountability_partners(partner_id);
create index idx_accountability_partners_status on public.accountability_partners(status);
create index idx_shared_habits_partnership on public.shared_habits(partnership_id);
create index idx_partner_activities_partnership on public.partner_activities(partnership_id);
create index idx_partner_activities_created on public.partner_activities(created_at desc);

-- RLS Policies
alter table public.accountability_partners enable row level security;
alter table public.shared_habits enable row level security;
alter table public.partner_activities enable row level security;

-- Accountability partners policies
create policy "Users can view their own partnerships"
  on public.accountability_partners for select
  using (auth.uid() = requester_id or auth.uid() = partner_id);

create policy "Users can create partnership requests"
  on public.accountability_partners for insert
  with check (auth.uid() = requester_id);

create policy "Users can update partnerships they're part of"
  on public.accountability_partners for update
  using (auth.uid() = requester_id or auth.uid() = partner_id);

-- Shared habits policies
create policy "Users can view shared habits in their partnerships"
  on public.shared_habits for select
  using (
    exists (
      select 1 from public.accountability_partners ap
      where ap.id = partnership_id
      and (ap.requester_id = auth.uid() or ap.partner_id = auth.uid())
      and ap.status = 'accepted'
    )
  );

create policy "Users can share their own habits"
  on public.shared_habits for insert
  with check (
    exists (
      select 1 from public.habits h
      where h.id = habit_id
      and h.user_id = auth.uid()
    )
    and exists (
      select 1 from public.accountability_partners ap
      where ap.id = partnership_id
      and (ap.requester_id = auth.uid() or ap.partner_id = auth.uid())
      and ap.status = 'accepted'
    )
  );

-- Partner activities policies
create policy "Users can view activities in their partnerships"
  on public.partner_activities for select
  using (
    exists (
      select 1 from public.accountability_partners ap
      where ap.id = partnership_id
      and (ap.requester_id = auth.uid() or ap.partner_id = auth.uid())
      and ap.status = 'accepted'
    )
  );

create policy "Users can create activities in their partnerships"
  on public.partner_activities for insert
  with check (
    auth.uid() = actor_id
    and exists (
      select 1 from public.accountability_partners ap
      where ap.id = partnership_id
      and (ap.requester_id = auth.uid() or ap.partner_id = auth.uid())
      and ap.status = 'accepted'
    )
  );

-- Helper function to get active partnerships
create or replace function get_active_partnerships(user_uuid uuid)
returns table (
  partnership_id uuid,
  partner_id uuid,
  partner_email text,
  status text,
  created_at timestamp with time zone
)
language sql
security definer
as $$
  select 
    ap.id as partnership_id,
    case 
      when ap.requester_id = user_uuid then ap.partner_id
      else ap.requester_id
    end as partner_id,
    case 
      when ap.requester_id = user_uuid then p.email
      else r.email
    end as partner_email,
    ap.status,
    ap.created_at
  from public.accountability_partners ap
  left join auth.users r on r.id = ap.requester_id
  left join auth.users p on p.id = ap.partner_id
  where (ap.requester_id = user_uuid or ap.partner_id = user_uuid)
  and ap.status = 'accepted';
$$;