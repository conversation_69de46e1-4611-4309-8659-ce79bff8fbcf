import { BlogPost } from './types'
import { supabase } from './supabase'

export async function getPosts(): Promise<BlogPost[]> {
  try {
    const { data } = await supabase
      .from('blog_posts')
      .select('id, title, blocks, body_md, status')
      .order('created_at', { ascending: false })

    return (data || []).map((p: any) => ({
      id: p.id,
      title: p.title,
      blocks: p.blocks,
      body_md: p.body_md || '',
      status: p.status || 'draft',
    }))
  } catch {
    return []
  }
}

export async function addPost(post: BlogPost): Promise<BlogPost | undefined> {
  const { data } = await supabase
    .from('blog_posts')
    .insert({ title: post.title, blocks: post.blocks, body_md: post.body_md, status: post.status })
    .select('id, title, blocks, body_md, status')
    .single()

  if (data) {
    return { id: data.id, title: data.title, blocks: data.blocks, body_md: data.body_md, status: data.status }
  }
  return undefined
}

export async function getPost(id: string): Promise<BlogPost | undefined> {
  const { data } = await supabase
    .from('blog_posts')
    .select('id, title, blocks, body_md, status')
    .eq('id', id)
    .single()

  if (data) {
    return { id: data.id, title: data.title, blocks: data.blocks, body_md: data.body_md, status: data.status }
  }
  return undefined
}

export async function updatePost(post: BlogPost): Promise<void> {
  await supabase
    .from('blog_posts')
    .update({ title: post.title, blocks: post.blocks, body_md: post.body_md, status: post.status })
    .eq('id', post.id)
}

