"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  BookText, // Changed from CalendarDays for Journal
  CheckCircle2, // Changed from Package for Habits
  GitBranch, // GitHub-style habits
  Newspaper,
  Settings as SettingsIcon,
} from "lucide-react";

import { cn } from "@/lib/utils";
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";

const navItems = [
  { href: "/", label: "Dashboard", icon: LayoutDashboard, exact: true },
  { href: "/journal", label: "Journal", icon: BookText },
  { href: "/blog", label: "Blog", icon: Newspaper },
  { href: "/habits", label: "Habits", icon: CheckCircle2 },
  { href: "/habits-github", label: "GitHub Habits", icon: GitBranch },
  { href: "/settings", label: "Settings", icon: SettingsIcon },
];

export function SidebarNav() {
  const pathname = usePathname();

  return (
    <SidebarMenu>
      {navItems.map((item) => {
        const isActive = item.exact ? pathname === item.href : pathname.startsWith(item.href);
        // A simple way to handle auth pages not highlighting dashboard; can be improved
        const isAuthPage = pathname.startsWith("/login") || pathname.startsWith("/register");
        const effectiveIsActive = isAuthPage && item.href === "/" ? false : isActive;

        return (
          <SidebarMenuItem key={item.href}>
            <Link href={item.href} passHref legacyBehavior>
              <SidebarMenuButton
                asChild
                isActive={effectiveIsActive}
                tooltip={{ children: item.label, className: "font-headline" }}
              >
                <a>
                  <item.icon />
                  <span>{item.label}</span>
                </a>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        );
      })}
    </SidebarMenu>
  );
}
