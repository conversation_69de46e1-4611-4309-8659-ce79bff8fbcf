
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { ChevronLeft, Save, Tag, Smile } from "lucide-react";
import Link from "next/link";
import { addEntry } from "@/lib/journal";
import { Block } from "@/lib/types";

import { MoodSelector } from "@/components/mood-selector";

export default function NewJournalEntryPage() {
  const router = useRouter();
  const [title, setTitle] = useState("");
  const [blocks, setBlocks] = useState<Block[]>([
    { text: "", visibility: "private" },
  ]);
  const [mood, setMood] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await addEntry(title, blocks, mood);
    router.push("/journal");
  };

  const updateBlock = (index: number, field: keyof Block, value: string) => {
    setBlocks((prev) => {
      const copy = [...prev];
      copy[index] = { ...copy[index], [field]: value };
      return copy;
    });
  };

  const addBlockField = () => {
    setBlocks((prev) => [...prev, { text: "", visibility: "private" }]);
  };

  const removeBlock = (idx: number) => {
    setBlocks((prev) => prev.filter((_, i) => i !== idx));
  };

  return (
    <div className="container mx-auto py-2">
      <Link href="/journal" className="inline-flex items-center text-sm text-primary hover:underline mb-4">
        <ChevronLeft className="mr-1 h-4 w-4" />
        Back to Journal
      </Link>
      <Card className="w-full shadow-xl">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle className="text-2xl font-headline">New Journal Entry</CardTitle>
            <CardDescription>Capture your thoughts, feelings, and reflections.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="entry-title" className="text-base">Title (Optional)</Label>
              <Input
                id="entry-title"
                placeholder="A memorable day..."
                className="mt-1 text-base"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>
            <div className="space-y-4">
              <Label className="text-base">Blocks</Label>
              {blocks.map((block, i) => (
                <div key={i} className="space-y-2 border p-2 rounded">
                  <Textarea
                    placeholder="Write anything that comes to mind..."
                    className="min-h-[120px] text-base"
                    value={block.text}
                    onChange={(e) => updateBlock(i, 'text', e.target.value)}
                  />
                  <div className="flex items-center gap-2">
                    <Select
                      value={block.visibility}
                      onValueChange={(v) => updateBlock(i, 'visibility', v)}
                    >
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="Visibility" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="private">Private</SelectItem>
                        <SelectItem value="public">Public</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeBlock(i)}
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
              <Button type="button" variant="secondary" size="sm" onClick={addBlockField}>
                Add Block
              </Button>
            </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="mood-selector" className="text-base">Mood</Label>
              <div className="flex items-center gap-2 mt-1">
                <Smile className="h-5 w-5 text-muted-foreground" />
                <MoodSelector value={mood} onChange={setMood} />
              </div>
            </div>
            <div>
              <Label htmlFor="tags-input" className="text-base">Tags (Optional)</Label>
               <div className="flex items-center gap-2 mt-1">
                <Tag className="h-5 w-5 text-muted-foreground" />
                <Input id="tags-input" placeholder="e.g., work, personal, gratitude" className="text-base" />
              </div>
            </div>
          </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button variant="outline" asChild>
              <Link href="/journal">Cancel</Link>
            </Button>
            <Button type="submit">
              <Save className="mr-2 h-4 w-4" />
              Save Entry
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
