"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Activity, BarChart3, Archive, Search, X, Flame, Target } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { useHabits } from "@/hooks/use-habits";
import { useSearch } from "@/hooks/use-search";
import { AddHabitDialog } from "@/components/add-habit-dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { formatStreakText, getNextMilestone } from "@/lib/habit-utils";

export default function HabitsPage() {
  const { habits, loading, toggleCheckin } = useHabits()
  
  const { query, setQuery, filteredI<PERSON>s, hasQ<PERSON>y, resultsCount } = useSearch({
    items: habits,
    searchFields: ['name'],
    filterFn: (habit, searchQuery) => {
      return habit.name.toLowerCase().includes(searchQuery)
    }
  });

  if (loading) {
    return (
      <div className="container mx-auto py-2">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-muted-foreground">Loading habits...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-2">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
        <h1 className="text-3xl font-bold font-headline text-foreground">Habit Tracker</h1>
        <div className="flex gap-2 w-full sm:w-auto">
          <div className="relative flex-grow sm:flex-grow-0">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search habits..."
              className="pl-8 sm:w-[200px] md:w-[250px]"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
            />
            {hasQuery && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1 h-7 w-7 p-0"
                onClick={() => setQuery('')}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
          <AddHabitDialog />
        </div>
      </div>
      
      <Tabs defaultValue="active-habits" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 mb-6">
          <TabsTrigger value="active-habits">
            <Activity className="mr-2 h-4 w-4" /> Active Habits
          </TabsTrigger>
          <TabsTrigger value="progress-overview">
            <BarChart3 className="mr-2 h-4 w-4" /> Progress & Streaks
          </TabsTrigger>
          <TabsTrigger value="archived-habits">
             <Archive className="mr-2 h-4 w-4" /> Archived
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active-habits">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">
                Your Active Habits
                {hasQuery && (
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    ({resultsCount} result{resultsCount === 1 ? '' : 's'})
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                {hasQuery 
                  ? `Showing results for "${query}"`
                  : "Stay consistent and track your daily progress."
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px]">
              {filteredItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
                  {hasQuery ? (
                    <>
                      <p>No habits found for "{query}"</p>
                      <p className="text-sm mt-2">Try adjusting your search terms.</p>
                    </>
                  ) : habits.length === 0 ? (
                    <>
                      <p>You are not tracking any habits yet.</p>
                      <p className="text-sm mt-2">Click "Add New Habit" to get started.</p>
                    </>
                  ) : (
                    <>
                      <p>No matching habits found.</p>
                      <p className="text-sm mt-2">Try a different search term.</p>
                    </>
                  )}
                </div>
              ) : (
                <ul className="space-y-4">
                  {filteredItems.map((habit) => (
                    <li key={habit.id} className="flex items-center justify-between border-b pb-4 last:border-0">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium">{habit.name}</p>
                          {habit.stats.streak.current > 0 && (
                            <Badge variant="secondary" className="flex items-center gap-1">
                              <Flame className="h-3 w-3" />
                              {habit.stats.streak.current}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4">
                          <p className="text-sm text-muted-foreground">
                            {habit.frequency}x / week
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {formatStreakText(habit.stats.streak)}
                          </p>
                          {habit.stats.currentWeekCompletions}/{habit.frequency} this week
                        </div>
                      </div>
                      <Checkbox
                        checked={habit.isCompletedToday}
                        onCheckedChange={() => toggleCheckin(habit.id)}
                        aria-label={`Mark ${habit.name} done today`}
                      />
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress-overview">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Progress & Streaks</CardTitle>
              <CardDescription>Visualize your consistency and celebrate milestones.</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px]">
              {filteredItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
                  <p>Add some habits to see progress charts.</p>
                </div>
              ) : (
                <ul className="space-y-6">
                  {filteredItems.map((habit) => (
                    <li key={habit.id} className="space-y-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{habit.name}</span>
                            {habit.stats.streak.current > 0 && (
                              <Badge variant="secondary" className="flex items-center gap-1">
                                <Flame className="h-3 w-3" />
                                {habit.stats.streak.current} day streak
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>This week: {habit.stats.currentWeekCompletions}/{habit.frequency}</span>
                            <span>Total: {habit.stats.totalCompletions} completions</span>
                            {habit.stats.streak.longest > habit.stats.streak.current && (
                              <span>Best: {habit.stats.streak.longest} days</span>
                            )}
                          </div>
                        </div>
                        <div className="text-right text-sm">
                          <div className="font-medium">{Math.round(habit.stats.completionRate)}%</div>
                          <div className="text-muted-foreground">complete</div>
                        </div>
                      </div>
                      
                      <Progress value={habit.stats.completionRate} className="h-2" />
                      
                      {habit.stats.streak.current > 0 && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Target className="h-4 w-4" />
                          <span>
                            Next milestone: {getNextMilestone(habit.stats.streak.current)} days
                            ({getNextMilestone(habit.stats.streak.current) - habit.stats.streak.current} more to go)
                          </span>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="archived-habits">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Archived Habits</CardTitle>
              <CardDescription>View habits you've completed or paused.</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <p>No archived habits.</p>
              <p className="text-sm mt-2">Completed habits will appear here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
