"use client"

import React, { useState, useMemo, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Book, GitCommit, AlertCircle, Settings, Plus, Search,
  Filter, Star, GitFork, Users, TrendingUp
} from 'lucide-react'
import { useHabits } from '@/hooks/use-habits'
import { useAuth } from '@/components/auth-provider'
import { ContributionGraph } from '@/components/github/ContributionGraph'
import { RepositoryCard, Repository } from '@/components/github/RepositoryCard'
import { IssueCard, Issue } from '@/components/github/IssueCard'
import { ActivityFeed, Activity } from '@/components/github/ActivityFeed'
import { ProfileSidebar } from '@/components/github/ProfileSidebar'
import { NewRepositoryDialog, NewHabitData } from '@/components/github/NewRepositoryDialog'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { calculateAchievements, getNextAchievements } from '@/lib/achievements'
import { AccountabilityPartners } from '@/components/github/AccountabilityPartners'
import { useAccountabilityPartners } from '@/hooks/use-accountability-partners'
import { useHabitSocial } from '@/hooks/use-habit-social'
import { useRouter } from 'next/navigation'
import '@/components/github/github-theme.css'

// Helper to convert habits to repositories
function habitToRepository(habit: any): Repository {
  const languageMap: Record<string, { name: string; color: string }> = {
    'Exercise': { name: 'Physical', color: '#f1e05a' },
    'Meditation': { name: 'Mental', color: '#3572A5' },
    'Reading': { name: 'Learning', color: '#e34c26' },
    'Writing': { name: 'Creative', color: '#563d7c' },
    'Coding': { name: 'Technical', color: '#00ADD8' },
    'Default': { name: 'Lifestyle', color: '#178600' }
  }

  const category = Object.keys(languageMap).find(key => 
    habit.name.toLowerCase().includes(key.toLowerCase())
  ) || 'Default'

  return {
    id: habit.id,
    name: habit.name,
    description: `Track ${habit.name} ${habit.frequency}x per week`,
    visibility: 'public', // Could be based on habit settings
    stars: Math.floor(habit.stats.totalCompletions / 10), // 1 star per 10 completions
    forks: 0, // Could track shared habits
    commits: habit.stats.totalCompletions,
    language: languageMap[category],
    topics: [`${habit.frequency}x-week`, 'habit', category.toLowerCase()],
    lastCommit: habit.stats.streak.lastCompletionDate,
    streak: habit.stats.streak.current,
    collaborators: 0 // Could track accountability partners
  }
}

// Helper to generate contribution data
function generateContributionData(habits: any[]): Record<string, number> {
  const data: Record<string, number> = {}
  
  habits.forEach(habit => {
    habit.checkins?.forEach((date: string) => {
      if (!data[date]) data[date] = 0
      data[date]++
    })
  })
  
  return data
}

// Helper to generate daily tasks/issues
function generateDailyIssues(habits: any[]): Issue[] {
  const today = format(new Date(), 'yyyy-MM-dd')
  let issueNumber = 1
  
  return habits.map(habit => {
    const isCompleted = habit.isCompletedToday
    const shouldComplete = habit.stats.currentWeekCompletions < habit.frequency
    
    return {
      id: `${habit.id}-${today}`,
      number: issueNumber++,
      title: habit.name,
      description: `Complete your daily ${habit.name} habit`,
      status: isCompleted ? 'closed' : 'open',
      priority: shouldComplete ? 'high' : 'medium',
      labels: [`habit`, `${habit.frequency}x-week`],
      createdAt: new Date(),
      dueDate: new Date()
    }
  }).filter(issue => issue.status === 'open' || issue.priority === 'high')
}

// Helper to generate activity feed
function generateActivityFeed(habits: any[]): Activity[] {
  const activities: Activity[] = []
  const today = format(new Date(), 'yyyy-MM-dd')
  
  habits.forEach(habit => {
    // Recent commits
    habit.checkins?.slice(-5).forEach((date: string) => {
      activities.push({
        id: `commit-${habit.id}-${date}`,
        type: 'commit',
        user: { name: 'You' },
        habitName: habit.name,
        message: '',
        timestamp: new Date(date),
        metadata: { count: 1 }
      })
    })
    
    // Achievements
    if (habit.stats.streak.current === 7) {
      activities.push({
        id: `achievement-${habit.id}-7`,
        type: 'achievement',
        user: { name: 'You' },
        habitName: habit.name,
        message: '',
        timestamp: new Date(),
        metadata: { streak: 7 }
      })
    }
  })
  
  return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, 10)
}

export default function GitHubStyleHabitsPage() {
  const { user } = useAuth()
  const { habits, loading, toggleCheckin, createHabit } = useHabits()
  const {
    partners,
    pendingRequests,
    invitePartner,
    acceptRequest,
    declineRequest,
    removePartner
  } = useAccountabilityPartners()
  const { socialData, starHabit, forkHabit, fetchSocialData } = useHabitSocial()
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState('repositories')
  const [showNewRepoDialog, setShowNewRepoDialog] = useState(false)
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<'recent' | 'stars' | 'name'>('recent')
  const router = useRouter()
  
  // Fetch social data for all habits
  useEffect(() => {
    if (habits.length > 0) {
      fetchSocialData(habits.map(h => h.id))
    }
  }, [habits, fetchSocialData])

  // Convert habits to GitHub-style data
  const repositories = useMemo(() => 
    habits.map(habit => {
      const repo = habitToRepository(habit)
      const social = socialData[habit.id]
      return {
        ...repo,
        stars: social?.starCount || repo.stars,
        forks: social?.forkCount || 0,
        isStarred: social?.isStarred || false
      }
    }), [habits, socialData]
  )
  const contributionData = useMemo(() => generateContributionData(habits), [habits])
  const dailyIssues = useMemo(() => generateDailyIssues(habits), [habits])
  const activities = useMemo(() => generateActivityFeed(habits), [habits])
  
  // Calculate stats and achievements
  const { stats, userAchievements } = useMemo(() => {
    const totalCommits = habits.reduce((sum, h) => sum + h.stats.totalCompletions, 0)
    const currentStreak = Math.max(...habits.map(h => h.stats.streak.current), 0)
    const longestStreak = Math.max(...habits.map(h => h.stats.streak.longest), 0)
    
    // Calculate aggregated stats for achievements
    const aggregatedStats = {
      totalCompletions: totalCommits,
      currentWeekCompletions: habits.reduce((sum, h) => sum + h.stats.currentWeekCompletions, 0),
      targetWeeklyCompletions: habits.reduce((sum, h) => sum + h.frequency, 0),
      weeklyCompletionRate: habits.length > 0 
        ? (habits.reduce((sum, h) => sum + h.stats.weeklyCompletionRate, 0) / habits.length)
        : 0,
      monthlyCompletionRate: habits.length > 0
        ? (habits.reduce((sum, h) => sum + h.stats.monthlyCompletionRate, 0) / habits.length)
        : 0,
      streak: {
        current: currentStreak,
        longest: longestStreak,
        lastCompletionDate: habits
          .map(h => h.stats.streak.lastCompletionDate)
          .filter(Boolean)
          .sort((a, b) => new Date(b!).getTime() - new Date(a!).getTime())[0] || null,
        breaks: 0
      }
    }
    
    const achievements = calculateAchievements(aggregatedStats)
    
    return {
      stats: {
        totalCommits,
        currentStreak,
        longestStreak,
        totalHabits: habits.length,
        achievements: achievements.length,
        collaborators: 0
      },
      userAchievements: achievements
    }
  }, [habits])
  
  const filteredRepos = repositories
    .filter(repo => {
      const matchesSearch = repo.name.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesCategory = !categoryFilter || repo.language?.name.toLowerCase() === categoryFilter.toLowerCase()
      return matchesSearch && matchesCategory
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'stars':
          return b.stars - a.stars
        case 'name':
          return a.name.localeCompare(b.name)
        case 'recent':
        default:
          return new Date(b.lastCommit || 0).getTime() - new Date(a.lastCommit || 0).getTime()
      }
    })

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-muted-foreground">Loading repositories...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-4" style={{ fontFamily: 'var(--gh-font-stack)' }}>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <ProfileSidebar
            user={{
              name: user?.email?.split('@')[0] || 'User',
              username: user?.email?.split('@')[0] || 'user',
              bio: 'Building better habits, one commit at a time.',
              status: `🔥 ${stats.currentStreak} day streak`
            }}
            stats={stats}
            achievements={userAchievements}
          />
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-2xl font-semibold mb-4">Habit Tracker</h1>
            
            {/* Search and filters */}
            <div className="flex gap-2 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Find a repository..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={categoryFilter || 'all'} onValueChange={(value) => setCategoryFilter(value === 'all' ? null : value)}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="physical">Physical</SelectItem>
                  <SelectItem value="mental">Mental</SelectItem>
                  <SelectItem value="learning">Learning</SelectItem>
                  <SelectItem value="creative">Creative</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                  <SelectItem value="lifestyle">Lifestyle</SelectItem>
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={(value: 'recent' | 'stars' | 'name') => setSortBy(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Sort" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Recent</SelectItem>
                  <SelectItem value="stars">Stars</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                </SelectContent>
              </Select>
              <Button 
                className="bg-[#2ea44f] hover:bg-[#2c974b]"
                onClick={() => setShowNewRepoDialog(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                New
              </Button>
            </div>

            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="bg-transparent border-b rounded-none h-auto p-0 w-full justify-start">
                <TabsTrigger 
                  value="overview" 
                  className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
                >
                  <Book className="w-4 h-4 mr-2" />
                  Overview
                </TabsTrigger>
                <TabsTrigger 
                  value="repositories"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
                >
                  <GitCommit className="w-4 h-4 mr-2" />
                  Repositories
                  <Badge variant="secondary" className="ml-2">{repositories.length}</Badge>
                </TabsTrigger>
                <TabsTrigger 
                  value="issues"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
                >
                  <AlertCircle className="w-4 h-4 mr-2" />
                  Issues
                  <Badge variant="secondary" className="ml-2">{dailyIssues.length}</Badge>
                </TabsTrigger>
                <TabsTrigger 
                  value="insights"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
                >
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Insights
                </TabsTrigger>
                <TabsTrigger 
                  value="partners"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-orange-600 rounded-none px-4 pb-2"
                >
                  <Users className="w-4 h-4 mr-2" />
                  Partners
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-6">
                <div className="space-y-6">
                  {/* Contribution Graph */}
                  <Card className="p-6">
                    <h3 className="text-sm font-medium mb-4">{stats.totalCommits} contributions in the last year</h3>
                    <ContributionGraph data={contributionData} weeks={52} />
                  </Card>

                  {/* Popular repositories */}
                  <div>
                    <h3 className="text-sm font-medium mb-3">Popular habits</h3>
                    <div className="grid gap-3">
                      {repositories.slice(0, 6).map(repo => (
                        <RepositoryCard
                          key={repo.id}
                          repository={repo}
                          onStar={() => starHabit(repo.id)}
                          onFork={() => forkHabit(repo.id)}
                          onClick={() => router.push(`/habits-github/${repo.id}`)}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Recent activity */}
                  <Card className="p-6">
                    <h3 className="text-sm font-medium mb-4">Recent activity</h3>
                    <ActivityFeed activities={activities} />
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="repositories" className="mt-6">
                <div className="space-y-3">
                  {filteredRepos.length === 0 ? (
                    <Card className="p-8 text-center">
                      <p className="text-muted-foreground">No repositories found</p>
                    </Card>
                  ) : (
                    filteredRepos.map(repo => (
                      <RepositoryCard
                        key={repo.id}
                        repository={repo}
                        onStar={() => starHabit(repo.id)}
                        onFork={() => forkHabit(repo.id)}
                        onClick={() => router.push(`/habits-github/${repo.id}`)}
                      />
                    ))
                  )}
                </div>
              </TabsContent>

              <TabsContent value="issues" className="mt-6">
                <Card>
                  <div className="p-4 border-b">
                    <h3 className="font-medium">Today's Tasks</h3>
                    <p className="text-sm text-muted-foreground">
                      {dailyIssues.filter(i => i.status === 'open').length} open issues
                    </p>
                  </div>
                  <div className="divide-y">
                    {dailyIssues.length === 0 ? (
                      <div className="p-8 text-center">
                        <p className="text-muted-foreground">All tasks completed! 🎉</p>
                      </div>
                    ) : (
                      dailyIssues.map(issue => (
                        <IssueCard
                          key={issue.id}
                          issue={issue}
                          onToggle={() => {
                            const habitId = issue.id.split('-')[0]
                            toggleCheckin(habitId)
                          }}
                        />
                      ))
                    )}
                  </div>
                </Card>
              </TabsContent>

              <TabsContent value="insights" className="mt-6">
                <div className="grid gap-6">
                  <Card className="p-6">
                    <h3 className="text-lg font-medium mb-4">Contribution Insights</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <p className="text-2xl font-bold">{stats.totalCommits}</p>
                        <p className="text-sm text-muted-foreground">Total Commits</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold">{stats.currentStreak}</p>
                        <p className="text-sm text-muted-foreground">Current Streak</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold">{stats.longestStreak}</p>
                        <p className="text-sm text-muted-foreground">Longest Streak</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold">{stats.totalHabits}</p>
                        <p className="text-sm text-muted-foreground">Active Habits</p>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6">
                    <h3 className="text-lg font-medium mb-4">Habit Categories</h3>
                    <div className="space-y-3">
                      {Object.entries(
                        repositories.reduce((acc, repo) => {
                          const lang = repo.language?.name || 'Other'
                          acc[lang] = (acc[lang] || 0) + 1
                          return acc
                        }, {} as Record<string, number>)
                      ).map(([category, count]) => (
                        <div key={category} className="flex items-center justify-between">
                          <span className="text-sm">{category}</span>
                          <div className="flex items-center gap-2">
                            <div className="w-32 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-green-600 h-2 rounded-full"
                                style={{ width: `${(count / repositories.length) * 100}%` }}
                              />
                            </div>
                            <span className="text-sm text-muted-foreground">{count}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="partners" className="mt-6">
                <AccountabilityPartners
                  partners={partners}
                  pendingRequests={pendingRequests}
                  onInvite={invitePartner}
                  onAccept={acceptRequest}
                  onDecline={declineRequest}
                  onRemove={removePartner}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* New Repository Dialog */}
      <NewRepositoryDialog
        open={showNewRepoDialog}
        onOpenChange={setShowNewRepoDialog}
        onSubmit={async (data: NewHabitData) => {
          await createHabit({
            name: data.name,
            frequency: data.frequency,
            created_at: new Date().toISOString(),
          })
          setShowNewRepoDialog(false)
        }}
      />
    </div>
  )
}