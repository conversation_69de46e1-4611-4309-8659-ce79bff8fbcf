-- Stars and Forks Schema for GitHub-style Habits

-- Stars table (users can star habits)
create table if not exists public.habit_stars (
  id uuid primary key default gen_random_uuid(),
  habit_id uuid references public.habits not null,
  user_id uuid references auth.users not null,
  created_at timestamp with time zone default now(),
  constraint unique_habit_star unique(habit_id, user_id)
);

-- Forks table (users can fork/copy habits)
create table if not exists public.habit_forks (
  id uuid primary key default gen_random_uuid(),
  original_habit_id uuid references public.habits not null,
  forked_habit_id uuid references public.habits not null,
  forked_by uuid references auth.users not null,
  created_at timestamp with time zone default now(),
  constraint unique_habit_fork unique(original_habit_id, forked_by)
);

-- Indexes for performance
create index idx_habit_stars_habit on public.habit_stars(habit_id);
create index idx_habit_stars_user on public.habit_stars(user_id);
create index idx_habit_forks_original on public.habit_forks(original_habit_id);
create index idx_habit_forks_forked on public.habit_forks(forked_habit_id);

-- R<PERSON> Policies
alter table public.habit_stars enable row level security;
alter table public.habit_forks enable row level security;

-- Stars policies
create policy "Users can view all stars"
  on public.habit_stars for select
  using (true);

create policy "Users can star habits"
  on public.habit_stars for insert
  with check (auth.uid() = user_id);

create policy "Users can unstar their own stars"
  on public.habit_stars for delete
  using (auth.uid() = user_id);

-- Forks policies
create policy "Users can view all forks"
  on public.habit_forks for select
  using (true);

create policy "Users can fork habits"
  on public.habit_forks for insert
  with check (auth.uid() = forked_by);

-- Function to fork a habit
create or replace function fork_habit(original_id uuid)
returns uuid
language plpgsql
security definer
as $$
declare
  new_habit_id uuid;
  original_habit record;
begin
  -- Get original habit details
  select * into original_habit
  from public.habits
  where id = original_id;
  
  if not found then
    raise exception 'Original habit not found';
  end if;
  
  -- Create new habit (fork)
  insert into public.habits (user_id, name, frequency, created_at)
  values (
    auth.uid(),
    original_habit.name || ' (fork)',
    original_habit.frequency,
    now()
  )
  returning id into new_habit_id;
  
  -- Record the fork relationship
  insert into public.habit_forks (original_habit_id, forked_habit_id, forked_by)
  values (original_id, new_habit_id, auth.uid());
  
  return new_habit_id;
end;
$$;

-- Function to get star count
create or replace function get_star_count(habit_id uuid)
returns integer
language sql
security definer
as $$
  select count(*)::integer
  from public.habit_stars
  where habit_stars.habit_id = get_star_count.habit_id;
$$;

-- Function to get fork count
create or replace function get_fork_count(habit_id uuid)
returns integer
language sql
security definer
as $$
  select count(*)::integer
  from public.habit_forks
  where habit_forks.original_habit_id = get_fork_count.habit_id;
$$;

-- Function to check if user has starred a habit
create or replace function has_user_starred(habit_id uuid, user_id uuid)
returns boolean
language sql
security definer
as $$
  select exists(
    select 1
    from public.habit_stars
    where habit_stars.habit_id = has_user_starred.habit_id
    and habit_stars.user_id = has_user_starred.user_id
  );
$$;