"use client"

import { useState, useEffect } from "react"
import { JournalEntry as RemoteEntry } from "@/lib/journal"
import { getAllEntries, JournalEntry as LocalEntry } from "@/lib/journal-store"
import { supabase } from "@/lib/supabase"

export interface Entry {
  id: string
  title: string
  content: string
  mood: string
  createdAt: string
  isLocal?: boolean
}


export function useEntries() {
  const [entries, setEntries] = useState<Entry[]>([])

  const fetchEntries = async () => {
    try {
      const { data } = await supabase
        .from('entries')
        .select('id, title, blocks, mood, created_at')
        .order('created_at', { ascending: false })

      const remote: Entry[] = (data || []).map((e: any) => ({
        id: e.id,
        title: e.title,
        content: e.blocks,
        mood: e.mood || "",
        createdAt: e.created_at,
        isLocal: false,
      }))

      const local: Entry[] = getAllEntries().map((e: LocalEntry) => ({
        id: `local-${e.id}`,
        title: e.text.slice(0, 20) || "Local Entry",
        content: e.text,
        mood: "",
        createdAt: e.createdAt,
        isLocal: true,
      }))

      const all = [...local, ...remote].sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )

      setEntries(all)
    } catch {
      setEntries([])
    }
  }

  useEffect(() => {
    fetchEntries()
  }, [])

  const createEntry = async (title: string, content: any, mood: string) => {
    const { data } = await supabase
      .from('entries')
      .insert({ title, blocks: content, mood })
      .select('id, title, blocks, mood, created_at')
      .single()

    const entry = {
      id: data?.id,
      title: data?.title,
      content: data?.blocks,
      mood: data?.mood || "",
      createdAt: data?.created_at,
      isLocal: false,
    } as Entry
    setEntries((prev) => [entry, ...prev])
    return entry
  }

  return { entries, createEntry, refresh: fetchEntries }
}

