import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import HabitsPage from '@/app/(main)/habits/page'
import { useHabits } from '@/hooks/use-habits'

jest.mock('@/hooks/use-habits')

const mockedUseHabits = useHabits as jest.MockedFunction<typeof useHabits>

test('displays list of habits', () => {
  mockedUseHabits.mockReturnValue({
    habits: [
      { id: '1', name: 'Drink Water', frequency: 7, checkins: [] },
      { id: '2', name: 'Exercise', frequency: 3, checkins: [] },
    ],
    addHabit: jest.fn(),
    toggleCheckin: jest.fn(),
  })
  render(<HabitsPage />)
  expect(screen.getByText('Drink Water')).toBeInTheDocument()
  expect(screen.getByText('Exercise')).toBeInTheDocument()
})

test('opens add habit dialog', async () => {
  mockedUseHabits.mockReturnValue({
    habits: [],
    addHabit: jest.fn(),
    toggleCheckin: jest.fn(),
  })
  render(<HabitsPage />)
  await userEvent.click(
    screen.getByRole('button', { name: /add new habit/i })
  )
  expect(screen.getByText('Create Habit')).toBeInTheDocument()
})
