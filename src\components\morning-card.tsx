import { generateMorningPrompt } from "@/ai/flows/generate-morning-prompt";
import MorningCardClient from "./morning-card-client";

export async function MorningCard() {
  let prompt = "What are you grateful for today, and what is your top priority?";
  try {
    prompt = await generateMorningPrompt();
  } catch (error) {
    console.error("Failed to generate morning prompt:", error);
  }

  return <MorningCardClient prompt={prompt} />;
}
