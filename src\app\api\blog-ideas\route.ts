import { NextResponse } from 'next/server';
import { generateBlogIdeas } from '@/ai/flows/generate-blog-ideas';
import { requireAuth, createErrorResponse } from '@/lib/auth-server';
import { aiRateLimiter, createRateLimitResponse } from '@/lib/rate-limit';
import { z } from 'zod';

const topicSchema = z.string().min(1).max(200);

export async function GET(request: Request) {
  try {
    // Require authentication
    const session = await requireAuth();
    
    // Apply rate limiting
    const rateLimitResult = await aiRateLimiter.check(session.user.id);
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult);
    }
    
    // Parse and validate topic
    const { searchParams } = new URL(request.url);
    const topic = searchParams.get('topic') || '';
    const validatedTopic = topicSchema.parse(topic);
    
    // Generate ideas
    const ideas = await generateBlogIdeas({ topic: validatedTopic });
    
    return NextResponse.json({ ideas });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse('Topic must be between 1 and 200 characters', 400);
    }
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    console.error('Failed to generate blog ideas', error);
    return createErrorResponse('Failed to generate blog ideas', 500);
  }
}
