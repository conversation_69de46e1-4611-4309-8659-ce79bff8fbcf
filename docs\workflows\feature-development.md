# Feature Development Workflow

## Overview

This guide provides step-by-step instructions for adding new features to LifeSync, following the established patterns and architecture of the codebase.

## Prerequisites

Before starting feature development:

1. **Environment Setup**:
   - Node.js and npm installed
   - Environment variables configured (.env.local)
   - Database access via Supabase
   - Genkit API key for AI features

2. **Development Commands**:
   ```bash
   npm run dev          # Start Next.js dev server
   npm run genkit:dev   # Start AI service (if needed)
   npm run lint         # Check code quality
   npm run typecheck    # Verify TypeScript
   npm test            # Run tests
   ```

## Feature Development Process

### 1. Planning Phase

#### Define the Feature
- Identify user needs and goals
- Determine which module(s) will be affected
- Plan data model requirements
- Consider AI integration opportunities
- Design UI/UX mockups

#### Architecture Decisions
- Choose appropriate route group: (main), (auth), or public
- Decide on state management approach
- Plan component structure
- Identify reusable components

### 2. Database Schema

#### Adding New Tables

1. **Design the Schema**:
   ```sql
   create table public.feature_name (
     id uuid primary key default gen_random_uuid(),
     user_id uuid references auth.users not null,
     -- your fields here
     created_at timestamp with time zone default now()
   );
   ```

2. **Add Row Level Security**:
   ```sql
   -- Standard RLS policies
   create policy "Users can view own data" on feature_name
     for select using (auth.uid() = user_id);
   
   create policy "Users can insert own data" on feature_name
     for insert with check (auth.uid() = user_id);
   
   create policy "Users can update own data" on feature_name
     for update using (auth.uid() = user_id);
   ```

3. **Apply Migration**:
   - Use Supabase dashboard or CLI
   - Test policies thoroughly
   - Document schema changes

#### Modifying Existing Tables

1. **JSONB Fields**: Add data without migration
2. **New Columns**: Create migration script
3. **Indexes**: Add for performance
4. **Constraints**: Ensure data integrity

### 3. Type Definitions

#### Create TypeScript Types

1. **Add to `src/lib/types.ts`**:
   ```typescript
   export interface FeatureName {
     id: string
     userId: string
     // your fields
     createdAt: string
   }
   ```

2. **Use Consistent Patterns**:
   - camelCase for TypeScript
   - snake_case for database
   - ISO strings for dates
   - Optional fields marked clearly

### 4. Data Layer

#### Create Data Access Functions

1. **Create `src/lib/feature-name.ts`**:
   ```typescript
   import { createClient } from '@/lib/supabase'
   
   export async function getFeatureItems() {
     const supabase = await createClient()
     const { data, error } = await supabase
       .from('feature_name')
       .select('*')
       .order('created_at', { ascending: false })
     
     if (error) throw error
     return data || []
   }
   
   export async function createFeatureItem(item: Omit<FeatureName, 'id' | 'createdAt'>) {
     const supabase = await createClient()
     const { data, error } = await supabase
       .from('feature_name')
       .insert(item)
       .select()
       .single()
     
     if (error) throw error
     return data
   }
   ```

#### Create Custom Hook

1. **Create `src/hooks/use-feature-name.ts`**:
   ```typescript
   export function useFeatureName() {
     const [items, setItems] = useState<FeatureName[]>([])
     
     useEffect(() => {
       loadItems()
     }, [])
     
     const loadItems = async () => {
       try {
         const data = await getFeatureItems()
         setItems(data)
       } catch (error) {
         console.error('Failed to load items:', error)
       }
     }
     
     const addItem = async (item: CreateFeatureItem) => {
       try {
         const newItem = await createFeatureItem(item)
         setItems(prev => [newItem, ...prev])
       } catch (error) {
         console.error('Failed to create item:', error)
       }
     }
     
     return { items, addItem, refresh: loadItems }
   }
   ```

### 5. UI Components

#### Page Structure

1. **Create Page Route**:
   ```
   src/app/(main)/feature-name/
   ├── page.tsx          # List/main view
   ├── new/
   │   └── page.tsx      # Create new item
   └── [id]/
       └── page.tsx      # View/edit item
   ```

2. **Implement List Page**:
   ```typescript
   export default function FeatureNamePage() {
     const { items } = useFeatureName()
     
     return (
       <div className="space-y-6">
         <div className="flex justify-between items-center">
           <h1 className="text-3xl font-bold">Feature Name</h1>
           <Button asChild>
             <Link href="/feature-name/new">
               <Plus className="mr-2 h-4 w-4" /> New Item
             </Link>
           </Button>
         </div>
         
         <div className="grid gap-4">
           {items.map(item => (
             <Card key={item.id}>
               {/* Item display */}
             </Card>
           ))}
         </div>
       </div>
     )
   }
   ```

#### Reusable Components

1. **Check Existing UI Components**:
   - Browse `src/components/ui/`
   - Use consistent patterns
   - Follow shadcn/ui conventions

2. **Create Feature Components**:
   ```
   src/components/
   └── feature-name/
       ├── feature-form.tsx
       ├── feature-card.tsx
       └── feature-dialog.tsx
   ```

### 6. Navigation Integration

#### Add to Sidebar

1. **Update `src/components/layout/sidebar-nav.tsx`**:
   ```typescript
   const items = [
     // ... existing items
     {
       title: "Feature Name",
       url: "/feature-name",
       icon: IconName,
     },
   ]
   ```

#### Add Dashboard Block (Optional)

1. **Update block types** in `dashboard-blocks.tsx`
2. **Create block component**
3. **Add to block registry**

### 7. AI Integration (If Applicable)

#### Create AI Flow

1. **Create `src/ai/flows/feature-ai-flow.ts`**:
   ```typescript
   import { defineFlow } from '@genkit-ai/flow'
   import { z } from 'zod'
   
   export const featureAIFlow = defineFlow(
     {
       name: 'featureAI',
       inputSchema: z.object({
         input: z.string(),
       }),
       outputSchema: z.string(),
     },
     async ({ input }) => {
       const response = await ai.generate({
         model: gemini,
         prompt: `Your prompt here: ${input}`,
       })
       
       return response.text
     }
   )
   ```

#### Create API Route

1. **Create `src/app/api/feature-ai/route.ts`**:
   ```typescript
   export async function POST(request: Request) {
     try {
       const { input } = await request.json()
       const result = await featureAIFlow({ input })
       return Response.json({ result })
     } catch (error) {
       return Response.json(
         { error: 'AI generation failed' },
         { status: 500 }
       )
     }
   }
   ```

### 8. Testing

#### Component Tests

1. **Create `tests/components/feature-name/`**
2. **Test user interactions**:
   ```typescript
   test('creates new item', async () => {
     render(<FeatureForm />)
     
     await userEvent.type(
       screen.getByLabelText(/name/i),
       'Test Item'
     )
     await userEvent.click(
       screen.getByRole('button', { name: /save/i })
     )
     
     expect(mockCreate).toHaveBeenCalledWith({
       name: 'Test Item'
     })
   })
   ```

#### Hook Tests

1. **Test data operations**
2. **Mock Supabase calls**
3. **Verify state updates**

### 9. Documentation

#### Update CLAUDE.md

Add your feature to the architecture section:
- New routes
- Database changes
- AI flows
- Common operations

#### Add Feature Documentation

Create `docs/modules/feature-name.md`:
- Feature overview
- User workflows
- Technical details
- Future enhancements

### 10. Deployment Checklist

Before deploying:

1. **Code Quality**:
   - [ ] Run `npm run lint`
   - [ ] Run `npm run typecheck`
   - [ ] Run `npm test`
   - [ ] Manual testing complete

2. **Database**:
   - [ ] Migrations applied
   - [ ] RLS policies tested
   - [ ] Indexes added if needed

3. **Environment**:
   - [ ] Environment variables documented
   - [ ] API keys configured
   - [ ] Feature flags set (if used)

4. **Documentation**:
   - [ ] CLAUDE.md updated
   - [ ] README updated if needed
   - [ ] API changes documented

## Common Patterns

### Error Handling

```typescript
try {
  // Operation
} catch (error) {
  console.error('Operation failed:', error)
  // Show user-friendly message
  toast.error('Something went wrong')
}
```

### Loading States

```typescript
const [loading, setLoading] = useState(false)

const handleAction = async () => {
  setLoading(true)
  try {
    await operation()
  } finally {
    setLoading(false)
  }
}
```

### Form Handling

```typescript
const [formData, setFormData] = useState({
  field1: '',
  field2: '',
})

const handleSubmit = async (e: FormEvent) => {
  e.preventDefault()
  await createItem(formData)
  // Reset or redirect
}
```

## Troubleshooting

### Common Issues

1. **TypeScript Errors**:
   - Check type definitions match database
   - Ensure all imports use `@/` prefix
   - Verify async function types

2. **Supabase Errors**:
   - Check RLS policies
   - Verify authentication
   - Test with Supabase dashboard

3. **UI Issues**:
   - Check Tailwind classes
   - Verify responsive design
   - Test dark mode

4. **State Problems**:
   - Check hook dependencies
   - Verify optimistic updates
   - Test error scenarios

## Best Practices

1. **Follow Existing Patterns**: Study similar features
2. **Keep It Simple**: Start minimal, enhance later
3. **Test Early**: Write tests as you code
4. **Document Well**: Future you will thank you
5. **Consider Mobile**: Test on small screens
6. **Think Security**: Never trust client input
7. **Plan for Scale**: Consider pagination early
8. **Use TypeScript**: Let the compiler help you

## Conclusion

Feature development in LifeSync follows consistent patterns that prioritize user experience, type safety, and maintainability. By following this workflow, new features integrate seamlessly with the existing architecture while maintaining code quality and user expectations.