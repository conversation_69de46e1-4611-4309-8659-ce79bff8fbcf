import { NextResponse } from 'next/server';
import { converse } from '@/ai/flows/conversation';
import { requireAuth, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth-server';
import { aiRateLimiter, createRateLimitResponse } from '@/lib/rate-limit';
import { z } from 'zod';

const chatRequestSchema = z.object({
  history: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string()
  })).default([]),
  message: z.string().min(1).max(5000)
});

export async function POST(req: Request) {
  try {
    // Require authentication
    const session = await requireAuth();
    
    // Apply rate limiting
    const rateLimitResult = await aiRateLimiter.check(session.user.id);
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult);
    }
    
    // Parse and validate request body
    const body = await req.json();
    const validatedData = chatRequestSchema.parse(body);
    
    // Process with AI
    const reply = await converse(validatedData);
    
    return NextResponse.json({ reply });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid request data', 400);
    }
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    console.error('Failed to generate chat reply', error);
    return createErrorResponse('Failed to generate response', 500);
  }
}
