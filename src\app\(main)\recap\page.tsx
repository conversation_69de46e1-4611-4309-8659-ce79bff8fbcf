'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function RecapPage() {
  const [summary, setSummary] = useState<string>('');

  useEffect(() => {
    fetch('/api/weekly-summary')
      .then(res => res.json())
      .then(data => setSummary(data.summary))
      .catch(() => setSummary(''));
  }, []);

  return (
    <div className="container mx-auto py-2">
      <h1 className="text-3xl font-bold font-headline text-foreground mb-8">
        Weekly Recap
      </h1>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="font-headline">This Week</CardTitle>
        </CardHeader>
        <CardContent>
          {summary ? (
            <p className="whitespace-pre-wrap text-muted-foreground">{summary}</p>
          ) : (
            <p className="text-muted-foreground">No recap available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
