"use client"

import { useState, useEffect } from "react"
import { BlogPost } from "@/lib/types"
import { supabase } from "@/lib/supabase"

export function useBlogPosts() {
  const [posts, setPosts] = useState<BlogPost[]>([])

  const fetchPosts = async () => {
    try {
      const { data } = await supabase
        .from('blog_posts')
        .select('id, title, blocks')
        .order('created_at', { ascending: false })

      const posts = (data || []).map((p: any) => ({
        id: p.id,
        title: p.title,
        blocks: p.blocks,
        status: 'public',
      }))
      setPosts(posts)
    } catch {
      setPosts([])
    }
  }

  useEffect(() => {
    fetchPosts()
  }, [])

  const createPost = async (title: string, blocks: any) => {
    const { data } = await supabase
      .from('blog_posts')
      .insert({ title, blocks })
      .select('id, title, blocks')
      .single()

    const post = {
      id: data?.id,
      title: data?.title,
      blocks: data?.blocks,
      status: 'public',
    } as BlogPost
    setPosts((prev) => [post, ...prev])
    return post
  }

  const updatePost = async (id: string, title: string, blocks: any) => {
    await supabase
      .from('blog_posts')
      .update({ title, blocks })
      .eq('id', id)
    setPosts((prev) => prev.map((p) => (p.id === id ? { ...p, title, blocks } : p)))
  }

  return { posts, createPost, updatePost, refresh: fetchPosts }
}

