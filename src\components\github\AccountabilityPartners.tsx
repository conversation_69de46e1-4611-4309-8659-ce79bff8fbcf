"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Users, UserPlus, Check, X, Clock, GitCommit } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { toast } from '@/hooks/use-toast'

export interface AccountabilityPartner {
  id: string
  partnerId: string
  partnerEmail: string
  partnerName?: string
  status: 'pending' | 'accepted' | 'declined'
  createdAt: Date
  sharedHabits?: number
  recentActivity?: {
    type: 'habit_completed' | 'streak_milestone'
    habitName: string
    timestamp: Date
  }
}

interface AccountabilityPartnersProps {
  partners: AccountabilityPartner[]
  pendingRequests: AccountabilityPartner[]
  onInvite: (email: string, message?: string) => Promise<void>
  onAccept: (partnerId: string) => Promise<void>
  onDecline: (partnerId: string) => Promise<void>
  onRemove: (partnerId: string) => Promise<void>
}

export function AccountabilityPartners({
  partners,
  pendingRequests,
  onInvite,
  onAccept,
  onDecline,
  onRemove
}: AccountabilityPartnersProps) {
  const [showInviteDialog, setShowInviteDialog] = useState(false)
  const [inviteEmail, setInviteEmail] = useState('')
  const [inviteMessage, setInviteMessage] = useState('')
  const [isInviting, setIsInviting] = useState(false)

  const handleInvite = async () => {
    if (!inviteEmail) return

    setIsInviting(true)
    try {
      await onInvite(inviteEmail, inviteMessage)
      setShowInviteDialog(false)
      setInviteEmail('')
      setInviteMessage('')
      toast({
        title: 'Invitation sent',
        description: `Partnership request sent to ${inviteEmail}`,
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send invitation. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsInviting(false)
    }
  }

  const getInitials = (email: string) => {
    return email.split('@')[0].slice(0, 2).toUpperCase()
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Accountability Partners
              </CardTitle>
              <CardDescription>
                Track habits together and stay motivated with partners
              </CardDescription>
            </div>
            <Button onClick={() => setShowInviteDialog(true)}>
              <UserPlus className="w-4 h-4 mr-2" />
              Invite Partner
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="active" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="active">
                Active Partners ({partners.length})
              </TabsTrigger>
              <TabsTrigger value="pending">
                Pending ({pendingRequests.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="mt-4">
              {partners.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No active partners yet</p>
                  <p className="text-sm mt-2">
                    Invite friends to track habits together!
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {partners.map(partner => (
                    <div
                      key={partner.id}
                      className="flex items-center justify-between p-4 rounded-lg border hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={`https://avatar.vercel.sh/${partner.partnerEmail}`} />
                          <AvatarFallback>{getInitials(partner.partnerEmail)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {partner.partnerName || partner.partnerEmail.split('@')[0]}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {partner.partnerEmail}
                          </div>
                          {partner.recentActivity && (
                            <div className="flex items-center gap-2 mt-1">
                              <GitCommit className="w-3 h-3 text-green-600" />
                              <span className="text-xs text-muted-foreground">
                                {partner.recentActivity.habitName} •{' '}
                                {formatDistanceToNow(partner.recentActivity.timestamp, { addSuffix: true })}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {partner.sharedHabits && partner.sharedHabits > 0 && (
                          <Badge variant="secondary">
                            {partner.sharedHabits} shared habits
                          </Badge>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemove(partner.id)}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="pending" className="mt-4">
              {pendingRequests.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No pending requests</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {pendingRequests.map(request => (
                    <div
                      key={request.id}
                      className="flex items-center justify-between p-4 rounded-lg border"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={`https://avatar.vercel.sh/${request.partnerEmail}`} />
                          <AvatarFallback>{getInitials(request.partnerEmail)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {request.partnerEmail.split('@')[0]}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {request.partnerEmail}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            Requested {formatDistanceToNow(request.createdAt, { addSuffix: true })}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          onClick={() => onAccept(request.id)}
                        >
                          <Check className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onDecline(request.id)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Invite Dialog */}
      <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Accountability Partner</DialogTitle>
            <DialogDescription>
              Send an invitation to track habits together
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Email address</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="message">Message (optional)</Label>
              <Input
                id="message"
                placeholder="Let's track habits together!"
                value={inviteMessage}
                onChange={(e) => setInviteMessage(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInviteDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleInvite} disabled={!inviteEmail || isInviting}>
              {isInviting ? 'Sending...' : 'Send Invitation'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}