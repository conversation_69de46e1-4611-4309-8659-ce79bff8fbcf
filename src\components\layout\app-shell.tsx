import type { PropsWithChildren } from "react";
import {
  SidebarProvider,
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarInset,
  SidebarTrigger,
  SidebarRail,
} from "@/components/ui/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { BrainCircuit } from "lucide-react"; // Changed icon
import { SidebarNav } from "./sidebar-nav";

export function AppShell({ children }: PropsWithChildren) {
  return (
    <SidebarProvider defaultOpen>
      <Sidebar collapsible="icon" variant="sidebar">
        <SidebarRail />
        <SidebarHeader className="p-4">
          <Button variant="ghost" className="w-full justify-start gap-2 px-2">
            <BrainCircuit className="text-primary" /> {/* Changed icon */}
            <span className="font-headline text-lg font-semibold">LifeSync AI</span>
          </Button>
        </SidebarHeader>
        <SidebarContent>
          <SidebarNav />
        </SidebarContent>
      </Sidebar>
      <SidebarInset>
        <header className="sticky top-0 z-10 flex h-14 items-center gap-4 border-b bg-background/80 px-4 backdrop-blur-sm sm:h-16 sm:px-6 md:hidden">
          <SidebarTrigger />
          <div className="flex items-center gap-2">
            <BrainCircuit className="h-6 w-6 text-primary" /> {/* Changed icon */}
            <span className="font-headline text-xl font-semibold">LifeSync AI</span>
          </div>
        </header>
        <main className="flex-1 p-4 sm:p-6">{children}</main>
      </SidebarInset>
    </SidebarProvider>
  );
}
