"use client";

import { use<PERSON>ara<PERSON> } from "next/navigation";
import { useEffect, useState } from "react";
import DashboardBlocks from "@/components/dashboard-blocks";
import { usePublicPageLayout } from "@/hooks/use-public-page-layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import Link from "next/link";
import { ChevronLeft } from "lucide-react";
import { getPosts, BlogPost } from "@/lib/blog";

function PostsBlock() {
  const [posts, setPosts] = useState<BlogPost[]>([]);

  useEffect(() => {
    getPosts().then((all) => {
      setPosts(all.filter((p) => p.status === "public"));
    });
  }, []);

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="font-headline">Public Posts</CardTitle>
        <CardDescription>Posts visible on your public page.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {posts.length === 0 ? (
          <p className="text-muted-foreground">No public posts found.</p>
        ) : (
          posts.map((post) => (
            <div key={post.id} className="border-b pb-2 last:border-0">
              <Link href={`/blog/${post.id}`} className="font-medium hover:underline">
                {post.title || "Untitled Post"}
              </Link>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}

function AboutBlock() {
  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="font-headline">About Me</CardTitle>
        <CardDescription>Introduce yourself to visitors.</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">This is your bio.</p>
      </CardContent>
    </Card>
  );
}

const blockMap = {
  posts: <PostsBlock />,
  about: <AboutBlock />,
};

export default function EditPublicPage() {
  const params = useParams<{ username: string }>();

  return (
    <div className="container mx-auto py-2">
      <Link
        href={`/p/${params.username}`}
        className="inline-flex items-center text-sm text-primary hover:underline mb-4"
      >
        <ChevronLeft className="mr-1 h-4 w-4" /> Back to Public Page
      </Link>
      <h1 className="text-3xl font-bold font-headline mb-6">Edit Public Page</h1>
      <DashboardBlocks blocksMap={blockMap} layoutHook={usePublicPageLayout} />
    </div>
  );
}

