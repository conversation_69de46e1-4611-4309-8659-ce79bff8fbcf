import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import NewBlogPostPage from '@/app/(main)/blog/new/page'
import { addPost } from '@/lib/blog'

jest.mock('@/lib/blog', () => ({
  getPosts: jest.fn(),
  addPost: jest.fn(),
  getPost: jest.fn(),
  updatePost: jest.fn(),
}))

const mockedAddPost = addPost as jest.MockedFunction<typeof addPost>

test('creates a new blog post', async () => {
  mockedAddPost.mockResolvedValue(undefined)
  render(<NewBlogPostPage />)
  await userEvent.type(screen.getByLabelText(/title/i), 'My Post')
  await userEvent.type(screen.getByLabelText(/body/i), 'Content')
  await userEvent.click(screen.getByRole('button', { name: /save post/i }))
  expect(mockedAddPost).toHaveBeenCalledWith(
    expect.objectContaining({ title: 'My Post', body: 'Content', status: 'draft' })
  )
})

