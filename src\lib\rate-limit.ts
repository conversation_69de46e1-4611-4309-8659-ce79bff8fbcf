interface RateLimitOptions {
  interval: number; // Time window in milliseconds
  uniqueTokenPerInterval: number; // Max number of requests per interval
}

interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: number;
}

class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(private options: RateLimitOptions) {}

  async check(identifier: string): Promise<RateLimitResult> {
    const now = Date.now();
    const { interval, uniqueTokenPerInterval } = this.options;
    
    // Get existing requests for this identifier
    const userRequests = this.requests.get(identifier) || [];
    
    // Filter out requests outside the current interval
    const recentRequests = userRequests.filter(
      timestamp => now - timestamp < interval
    );
    
    // Check if limit exceeded
    if (recentRequests.length >= uniqueTokenPerInterval) {
      return {
        success: false,
        limit: uniqueTokenPerInterval,
        remaining: 0,
        reset: Math.min(...recentRequests) + interval
      };
    }
    
    // Add current request
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);
    
    // Clean up old entries periodically
    if (Math.random() < 0.01) {
      this.cleanup();
    }
    
    return {
      success: true,
      limit: uniqueTokenPerInterval,
      remaining: uniqueTokenPerInterval - recentRequests.length,
      reset: now + interval
    };
  }
  
  private cleanup() {
    const now = Date.now();
    const { interval } = this.options;
    
    for (const [identifier, requests] of this.requests.entries()) {
      const recentRequests = requests.filter(
        timestamp => now - timestamp < interval
      );
      
      if (recentRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, recentRequests);
      }
    }
  }
}

// Create rate limiters for different endpoints
export const aiRateLimiter = new RateLimiter({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 10 // 10 requests per minute
});

export const generalRateLimiter = new RateLimiter({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 60 // 60 requests per minute
});

// Helper function to apply rate limiting
export async function rateLimit(
  identifier: string,
  limiter: RateLimiter = generalRateLimiter
): Promise<RateLimitResult> {
  return limiter.check(identifier);
}

// Response helper for rate limit errors
export function createRateLimitResponse(result: RateLimitResult) {
  return new Response(
    JSON.stringify({
      error: 'Too many requests',
      retryAfter: Math.ceil((result.reset - Date.now()) / 1000)
    }),
    {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        'X-RateLimit-Limit': result.limit.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.reset).toISOString(),
        'Retry-After': Math.ceil((result.reset - Date.now()) / 1000).toString()
      }
    }
  );
}