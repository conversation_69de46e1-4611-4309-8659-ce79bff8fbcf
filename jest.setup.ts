import '@testing-library/jest-dom'

// Polyfill TextEncoder/TextDecoder for components relying on Web APIs
import { TextEncoder, TextDecoder } from 'util'

if (typeof global.TextEncoder === 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(global as any).TextEncoder = TextEncoder
}
if (typeof global.TextDecoder === 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(global as any).TextDecoder = TextDecoder
}

// Mock next/navigation hooks used in tests
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}))

// Mock supabase client used in hooks/pages
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: () => ({
      select: () => ({
        order: async () => ({ data: [] }),
        single: async () => ({ data: undefined }),
      }),
      insert: () => ({
        select: () => ({
          single: async () => ({ data: undefined }),
        }),
      }),
      update: () => ({
        select: () => ({
          single: async () => ({ data: undefined }),
        }),
      }),
      eq: () => ({
        select: () => ({
          single: async () => ({ data: undefined }),
        }),
      }),
    }),
  },
}))
