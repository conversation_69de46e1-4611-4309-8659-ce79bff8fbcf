# Data Flow Documentation

## Overview

This document traces how data flows through the LifeSync application, from user interaction to database storage and back to the UI. Understanding these patterns is crucial for debugging, performance optimization, and feature development.

## High-Level Data Flow

```
User Interaction
       ↓
React Component
       ↓
Custom Hook / Event Handler
       ↓
Supabase Client / API Route
       ↓
Database / AI Service
       ↓
Response Processing
       ↓
State Update
       ↓
UI Re-render
```

## Detailed Flow Patterns

### 1. Standard CRUD Operations

#### Create Flow (Example: New Journal Entry)

```
1. User fills form in UI component
   └─> Local state updates (controlled inputs)

2. User clicks "Save" button
   └─> Form submission handler triggered

3. <PERSON><PERSON> calls custom hook method
   └─> useEntries.createEntry(data)

4. <PERSON> performs optimistic update
   └─> setState(prev => [...prev, newEntry])
   └─> UI updates immediately

5. <PERSON> calls Supabase client
   └─> supabase.from('entries').insert(data)

6. Database processes request
   └─> RLS policies check user permissions
   └─> Data inserted with generated ID
   └─> Timestamps added automatically

7. Response returned to hook
   └─> Success: Data confirmed
   └─> Error: Silent failure (current pattern)

8. UI reflects final state
   └─> Navigation to entry list
   └─> Toast notification (optional)
```

#### Read Flow (Example: Loading Habits)

```
1. Component mounts
   └─> useEffect triggered

2. Custom hook fetches data
   └─> useHabits calls loadHabits()

3. Supabase query executed
   └─> SELECT * FROM habits WHERE user_id = auth.uid()
   └─> RLS automatically filters by user

4. Data transformation
   └─> Database snake_case to JavaScript camelCase
   └─> Date strings parsed if needed

5. State updated
   └─> setHabits(transformedData)

6. Component re-renders
   └─> Maps over habits array
   └─> Displays each habit with current state
```

#### Update Flow (Example: Toggle Habit Check-in)

```
1. User clicks checkbox
   └─> onClick handler triggered

2. Calculate new state
   └─> Check if date exists in checkins array
   └─> Add or remove date accordingly

3. Optimistic update
   └─> Update local state immediately
   └─> UI shows checked/unchecked instantly

4. Persist to database
   └─> Update habits table checkins array
   └─> Create habit_event record

5. Handle response
   └─> Success: State remains
   └─> Failure: Should rollback (not implemented)
```

### 2. AI-Enhanced Flows

#### AI Generation Flow (Example: Morning Prompt)

```
1. Server Component renders
   └─> MorningCard component

2. Direct AI flow call (server-side)
   └─> generateMorningPrompt()

3. Genkit processes request
   └─> Sends prompt to Gemini 2.0 Flash
   └─> Receives generated text

4. Response handling
   └─> Success: Display AI prompt
   └─> Failure: Show fallback prompt

5. No client-state needed
   └─> Server-rendered content
   └─> Cached naturally by React
```

#### AI API Flow (Example: Blog Ideas)

```
1. User clicks "AI Assist"
   └─> Client-side button

2. API call initiated
   └─> POST /api/blog-ideas
   └─> Body: { topic: "..." }

3. API route processes
   └─> Parses request body
   └─> Calls Genkit flow

4. AI generation
   └─> Flow sends to Gemini
   └─> Structured response returned

5. API returns JSON
   └─> { ideas: ["...", "...", "..."] }

6. Client updates UI
   └─> Displays suggestions
   └─> User selects one
```

### 3. Complex Data Flows

#### Dashboard Layout Persistence

```
1. User drags block
   └─> @dnd-kit captures interaction

2. Calculate new position
   └─> Array index manipulation

3. Update local state
   └─> setBlocks(reorderedArray)
   └─> UI updates instantly

4. Persist to storage (dual)
   └─> localStorage.setItem() - immediate
   └─> supabase.upsert() - async

5. Next session load
   └─> Try Supabase first
   └─> Fall back to localStorage
   └─> Use defaults if neither exists
```

#### Journal to Blog Transformation

```
1. User creates journal entry
   └─> Blocks marked private/public
   └─> Saved to entries table

2. User converts to blog
   └─> Creates new blog post
   └─> Links via entry_id

3. Block visibility applied
   └─> Private blocks filtered
   └─> Public blocks displayed

4. Publishing flow
   └─> Status: draft → public
   └─> Available on profile
```

### 4. Authentication Flow

#### Login Data Flow

```
1. User enters credentials
   └─> Login form submission

2. Supabase Auth called
   └─> signInWithPassword()

3. Session established
   └─> Tokens stored by Supabase
   └─> User object returned

4. AuthContext updated
   └─> Global state change
   └─> All components re-render

5. Navigation triggered
   └─> Redirect to dashboard
   └─> Protected routes accessible

6. Subsequent requests
   └─> Auth headers included
   └─> RLS policies enforce access
```

### 5. Real-time Features (Future)

#### Potential Real-time Flow

```
1. Subscribe to changes
   └─> supabase.from('table').on('*', callback)

2. Another user makes change
   └─> Database updated
   └─> Change event broadcast

3. Subscription receives event
   └─> Callback triggered
   └─> Payload contains change data

4. Local state updated
   └─> Merge change into state
   └─> UI updates automatically
```

## Data Storage Patterns

### Primary Storage (Supabase)

```
User Action → API Call → Database → Response
                ↓
           RLS Policies
           Validate Access
```

**Characteristics**:
- Single source of truth
- Automatic user isolation
- JSONB for flexible schemas
- PostgreSQL reliability

### Fallback Storage (LocalStorage)

```
User Action → LocalStorage → Immediate Update
                ↓
          Background Sync
          (When Available)
```

**Use Cases**:
- Offline capability
- Draft preservation
- Layout preferences
- Quick reflections

### Hybrid Storage

```
        ┌─→ LocalStorage (Fast)
        │
Action ─┤
        │
        └─→ Supabase (Persistent)
                ↓
            Next Load
            Prioritizes
            Supabase
```

## State Synchronization

### Optimistic Updates

```
1. User Action
2. Update UI State ────────────→ Instant Feedback
3. Update Database ───┐
4. Await Response    │
5. Handle Success ←──┘
   OR
5. Rollback on Error (Not Implemented)
```

### Current Limitations

1. **No Rollback**: Failed updates leave inconsistent state
2. **No Conflict Resolution**: Last write wins
3. **No Offline Queue**: Actions lost if offline
4. **No Real-time Sync**: Manual refresh needed

## API Route Data Flow

### Standard API Pattern

```typescript
// 1. Request arrives
POST /api/feature
Body: { data }

// 2. Parse and validate
const body = await request.json()

// 3. Process (AI/Database)
const result = await processData(body)

// 4. Return response
return Response.json({ result })
```

### Error Handling

```
Try Operation
     ↓
  Success? ──Yes──→ Return Data
     ↓
    No
     ↓
Log Error
     ↓
Return Generic Error
(No details to client)
```

## Performance Considerations

### Current Bottlenecks

1. **Full Data Fetches**: No pagination
2. **No Caching**: Refetch on every mount
3. **Sequential Operations**: Could parallelize
4. **Large Payloads**: Entire objects transferred

### Optimization Opportunities

```
Current:
Component Mount → Fetch All Data → Render All

Optimized:
Component Mount → Fetch Page 1 → Render → Lazy Load More
                         ↓
                    Cache Result
                         ↓
                 Use Cache on Return
```

## Security in Data Flow

### Client to Server

```
User Input
    ↓
Client Validation (TypeScript)
    ↓
API Route
    ↓
Server Validation
    ↓
Sanitization
    ↓
Database Operation
```

### RLS Protection

```
Database Query
    ↓
Check auth.uid()
    ↓
Apply RLS Policy
    ↓
Filter Results
    ↓
Return User's Data Only
```

## Debugging Data Flow

### Trace Points

1. **Component Level**:
   ```typescript
   console.log('Component rendered with:', props)
   ```

2. **Hook Level**:
   ```typescript
   console.log('Hook state updated:', newState)
   ```

3. **API Level**:
   ```typescript
   console.log('API received:', await request.json())
   ```

4. **Database Level**:
   - Supabase Dashboard logs
   - Query performance metrics

### Common Issues

1. **State Not Updating**:
   - Check hook dependencies
   - Verify setState calls
   - Look for stale closures

2. **Data Not Persisting**:
   - Check RLS policies
   - Verify auth state
   - Inspect network calls

3. **Wrong Data Displayed**:
   - Check data transformations
   - Verify sorting/filtering
   - Look for race conditions

## Best Practices

### Data Fetching

1. **Fetch in Effects**: Use useEffect for side effects
2. **Handle Loading**: Show skeletons/spinners
3. **Handle Errors**: At least log them
4. **Clean Up**: Cancel requests on unmount

### State Updates

1. **Immutable Updates**: Don't mutate state
2. **Batch Updates**: Let React batch automatically
3. **Derive State**: Calculate vs store when possible
4. **Minimize State**: Only store what you need

### Performance

1. **Lazy Load**: Load data as needed
2. **Debounce**: For search/filter operations
3. **Memoize**: Expensive calculations
4. **Paginate**: Large data sets

## Future Improvements

### Enhanced Data Flow

```
Future Architecture:
User Action
    ↓
Local State + Optimistic Update
    ↓
Background Queue (Persistent)
    ↓
Sync Service (With Retry)
    ↓
Conflict Resolution
    ↓
Real-time Updates to All Clients
```

### Key Additions

1. **Request Deduplication**: Cache and share responses
2. **Offline Support**: Queue and sync when online
3. **Real-time Updates**: WebSocket subscriptions
4. **Better Error Handling**: User-friendly messages
5. **Performance Monitoring**: Track slow operations

## Conclusion

Understanding LifeSync's data flow patterns is essential for effective development. While the current implementation favors simplicity, there are clear paths for enhancement that would improve reliability, performance, and user experience. The modular architecture makes these improvements feasible without major refactoring.