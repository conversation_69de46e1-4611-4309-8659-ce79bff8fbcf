"use client";

import { useParams } from "next/navigation";

import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { getPosts, BlogPost } from "@/lib/blog";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";

const CURRENT_USER_ID = "00000000-0000-0000-0000-000000000001";

export default function PublicProfilePage() {
  const params = useParams<{ username: string }>();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [isOwner, setIsOwner] = useState(false);
  const [following, setFollowing] = useState(false);


  useEffect(() => {
    getPosts().then((all) => {
      setPosts(all.filter((p) => p.status === "public"));
    });
  }, []);

  useEffect(() => {
    try {
      const current = localStorage.getItem("currentUser");
      setIsOwner(current === params.username);
    } catch {
      setIsOwner(false);
    }
  }, [params.username]);

  useEffect(() => {
    supabase
      .from('followers')
      .select('*')
      .eq('follower_id', CURRENT_USER_ID)
      .eq('followed_id', params.username)
      .maybeSingle()
      .then(({ data }) => setFollowing(!!data))
      .catch(() => setFollowing(false))
  }, [params.username])

  const handleFollow = async () => {
    if (following) {
      await supabase
        .from('followers')
        .delete()
        .eq('follower_id', CURRENT_USER_ID)
        .eq('followed_id', params.username)
      setFollowing(false)
    } else {
      await supabase
        .from('followers')
        .insert({ follower_id: CURRENT_USER_ID, followed_id: params.username })
      setFollowing(true)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-3xl font-headline font-bold">
          {params.username}'s Public Posts
        </h1>
        {isOwner ? (
          <Button variant="outline" asChild>
            <Link href={`/p/${params.username}/edit`}>Edit Public Page</Link>
          </Button>
        ) : (
          <Button variant="outline" onClick={handleFollow}>
            {following ? "Unfollow" : "Follow"}
          </Button>
        )}
      </div>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="font-headline">Posts</CardTitle>
          <CardDescription>Posts marked as public.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {posts.length === 0 ? (
            <p className="text-muted-foreground">No public posts found.</p>
          ) : (
            posts.map((post) => (
              <div key={post.id} className="border-b pb-2 last:border-0">
                <Link href={`/blog/${post.id}`} className="font-medium hover:underline">
                  {post.title || "Untitled Post"}
                </Link>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  );
}
