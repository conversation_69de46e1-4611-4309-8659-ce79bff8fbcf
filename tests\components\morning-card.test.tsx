import { render, screen } from '@testing-library/react'
import { MorningCard } from '@/components/morning-card'

jest.mock('@/ai/flows/generate-morning-prompt', () => ({
  generateMorningPrompt: jest.fn().mockResolvedValue('Test prompt.')
}))
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn()
}))

test('renders morning reflection card with prompt', async () => {
  const element = await MorningCard()
  render(element)
  expect(screen.getByText('Morning Reflection')).toBeInTheDocument()
  expect(screen.getByText('Test prompt.')).toBeInTheDocument()
})
