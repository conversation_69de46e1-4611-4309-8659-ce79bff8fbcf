export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      entries: {
        Row: {
          id: string
          user_id: string
          title: string | null
          blocks: Json
          mood: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title?: string | null
          blocks: Json
          mood?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string | null
          blocks?: Json
          mood?: string | null
          created_at?: string
        }
      }
      habits: {
        Row: {
          id: string
          user_id: string
          name: string
          frequency: number
          checkins: string[]
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          frequency: number
          checkins?: string[]
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          frequency?: number
          checkins?: string[]
          created_at?: string
        }
      }
      habit_events: {
        Row: {
          id: string
          user_id: string
          habit_id: string
          event_date: string
          completed: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          habit_id: string
          event_date: string
          completed: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          habit_id?: string
          event_date?: string
          completed?: boolean
          created_at?: string
        }
      }
      blog_posts: {
        Row: {
          id: string
          user_id: string
          entry_id: string | null
          title: string
          blocks: Json
          body_md: string | null
          status: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          entry_id?: string | null
          title: string
          blocks: Json
          body_md?: string | null
          status?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          entry_id?: string | null
          title?: string
          blocks?: Json
          body_md?: string | null
          status?: string
          created_at?: string
        }
      }
      moods: {
        Row: {
          id: string
          user_id: string
          rating: number
          note: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          rating: number
          note?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          rating?: number
          note?: string | null
          created_at?: string
        }
      }
      followers: {
        Row: {
          follower_id: string
          followed_id: string
          timestamp: string
        }
        Insert: {
          follower_id: string
          followed_id: string
          timestamp?: string
        }
        Update: {
          follower_id?: string
          followed_id?: string
          timestamp?: string
        }
      }
      dashboard_layouts: {
        Row: {
          id: string
          user_id: string
          blocks: Json | null
          public_blocks: Json | null
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          blocks?: Json | null
          public_blocks?: Json | null
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          blocks?: Json | null
          public_blocks?: Json | null
          updated_at?: string
        }
      }
      weekly_summaries: {
        Row: {
          id: string
          summary: string
          created_at: string
        }
        Insert: {
          id?: string
          summary: string
          created_at?: string
        }
        Update: {
          id?: string
          summary?: string
          created_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          username: string | null
          email: string | null
          onboarding_completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username?: string | null
          email?: string | null
          onboarding_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string | null
          email?: string | null
          onboarding_completed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}