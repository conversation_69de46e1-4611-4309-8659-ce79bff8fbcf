import { Suspense } from "react";
import { MorningCard } from "@/components/morning-card"; // This will evolve or be replaced by Journal/Dashboard blocks
import { RapidLog } from "@/components/rapid-log"; // This will evolve or be replaced
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Bar<PERSON>hart, LayoutGrid, ListChecks, Settings, Users } from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import LatestReflectionCard from "@/components/latest-reflection-card";
import DashboardBlocks from "@/components/dashboard-blocks";
import HabitInsightsCard from "@/components/habit-insights-card";


// Placeholder blocks for the new dashboard
const PlaceholderBlock = ({ title, description, icon: Icon, link }: { title: string; description: string; icon: React.ElementType; link?: string }) => (
  <Card className="shadow-lg hover:shadow-xl transition-shadow">
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-lg font-medium font-headline">{title}</CardTitle>
      <Icon className="h-5 w-5 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <p className="text-sm text-muted-foreground mb-4">{description}</p>
      {link && (
        <Button variant="outline" size="sm" asChild>
          <Link href={link}>View More</Link>
        </Button>
      )}
    </CardContent>
  </Card>
);

const blockMap = {
  journal: (
    <Suspense fallback={<Skeleton className="h-[200px] w-full rounded-lg" />}>
      <Card className="shadow-lg col-span-1 lg:col-span-2">
        <CardHeader>
          <CardTitle className="font-headline">Quick Journal / Reflection</CardTitle>
        </CardHeader>
        <CardContent>
          <MorningCard />
        </CardContent>
      </Card>
    </Suspense>
  ),
  habits: (
    <PlaceholderBlock
      title="Quick Add / Habits"
      description="Log tasks, ideas, or check-in habits quickly."
      icon={ListChecks}
      link="/habits"
    />
  ),
  mood: (
    <PlaceholderBlock
      title="Mood Overview"
      description="Track and visualize your mood patterns."
      icon={Users}
    />
  ),
  insights: (
    <HabitInsightsCard />
  ),
  activity: (
    <PlaceholderBlock
      title="Recent Activity"
      description="Your latest entries and achievements."
      icon={LayoutGrid}
    />
  ),
} as const;


export default function DashboardPage() {
  return (
    <div className="container mx-auto py-2">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold font-headline text-foreground">
          Dashboard
        </h1>
        <Button variant="outline">
          <LayoutGrid className="mr-2 h-4 w-4" /> Customize Layout
        </Button>
      </div>

      <LatestReflectionCard />
      
      <DashboardBlocks blocksMap={blockMap} />

      {/* The existing RapidLog component, can be integrated or removed later */}
      {/* <div className="mt-8">
        <RapidLog />
      </div> */}
    </div>
  );
}
