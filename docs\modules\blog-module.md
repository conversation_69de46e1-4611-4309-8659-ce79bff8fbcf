# Blog Module Documentation

## Overview

The Blog module transforms LifeSync from a personal journaling tool into a platform for public expression. It enables users to selectively share their insights, experiences, and growth journey while maintaining complete control over their privacy.

## Core Philosophy

The Blog module embodies the principle of "selective transparency":
- **Privacy First**: All content is private by default
- **Granular Control**: Block-level visibility settings
- **Seamless Transformation**: Journal entries can become blog posts
- **Public Presence**: Build a following while maintaining boundaries

## Architecture Design

### Content Structure

The blog module inherits and extends the block-based architecture:
- **Blocks**: Same structure as journal entries
- **Dual Format**: Supports both blocks and markdown
- **Status Management**: Draft and public states
- **Entry Linking**: Optional connection to source journal entries

### Database Design

**blog_posts table**:
- Unique identifier (UUID)
- User association
- Optional journal entry reference
- Title (required)
- Blocks array (JSONB)
- Markdown body (optional)
- Status field (draft/public)
- Creation timestamp

This design enables:
- Flexible content creation
- Journal-to-blog workflow
- Multiple format support
- Publishing workflow

## Key Features

### 1. **Block-Based Privacy**
- Each content block has individual visibility
- Private blocks hidden from public view
- Public blocks displayed on profile
- Visual indicators in editor

### 2. **Publishing Workflow**
- Create posts as drafts
- Preview before publishing
- Switch between draft/public states
- Immediate updates

### 3. **AI-Assisted Writing**
- Generate blog post ideas
- Topic-based suggestions
- Three ideas per request
- Seamless editor integration

### 4. **Public Profiles**
- Unique username-based URLs
- Display only public posts
- Follow/unfollow capabilities
- Owner edit access

### 5. **Social Features**
- Following system
- Follower relationships
- Future: engagement metrics
- Future: comments/reactions

## User Workflows

### Creating a Blog Post

1. **Initiation**: Access blog section, click "New Post"
2. **Content Creation**:
   - Add compelling title
   - Create content blocks
   - Set visibility per block
   - Optional: Use AI for ideas
3. **Review**: Preview post appearance
4. **Publication**: Set status to public when ready

### Journal-to-Blog Transformation

1. **Selection**: Identify journal entry to share
2. **Conversion**: Create new blog post
3. **Curation**: Adjust block visibility
4. **Enhancement**: Add public-facing content
5. **Publication**: Share with audience

### Profile Management

1. **Access**: Navigate to public profile
2. **Customization**: Arrange content blocks
3. **Preview**: See public view
4. **Sharing**: Share profile URL

## Technical Implementation

### Component Architecture

**Page Components**:
- Blog list: Post management interface
- New post: Creation interface
- Edit post: Modification interface
- View post: Public display
- Public profile: User's public presence

**Key Features**:
- Dynamic block management
- Real-time preview
- Status toggling
- AI integration

### State Management

**useBlogPosts Hook**:
- Client-side blog post state
- CRUD operations
- Optimistic updates
- Note: Some inconsistencies with utilities

**Blog Utilities**:
- Server-integrated functions
- Direct Supabase operations
- Type-safe interfaces
- Consistent error handling

### Layout System

**Public Page Layouts**:
- Customizable block arrangement
- Posts block: Blog post list
- About block: Bio section (placeholder)
- Persistent storage in Supabase
- LocalStorage fallback

## AI Integration

### Blog Ideas Generation

**Flow**:
1. User provides topic/title
2. API route processes request
3. Genkit generates ideas
4. Three suggestions returned
5. User selects and expands

**Prompt Design**:
- Focuses on engaging topics
- Considers user's theme
- Provides diverse angles
- Actionable suggestions

## Security and Privacy

### Access Control

**Row-Level Security**:
- Users see only their posts in management
- Public posts need additional policies
- Owner-only edit permissions
- Follower relationship visibility

### Privacy Features
- Default draft status
- Block-level control
- Clear public/private indicators
- No accidental exposure

### Current Gaps
- Public post access not fully implemented
- Anonymous viewing needs policies
- API routes lack authentication
- Some hardcoded user references

## Social Features

### Following System

**Implementation**:
- Many-to-many follower relationships
- Timestamp tracking
- Bidirectional queries
- Real-time updates

**User Experience**:
- Follow from public profiles
- Unfollow capability
- Future: Follow notifications
- Future: Activity feeds

### Public Profiles

**Features**:
- Clean URL structure (/p/username)
- Customizable layout
- Public post display
- Professional appearance

**Limitations**:
- Basic layout options
- No rich profile information
- Limited customization
- No analytics

## Integration Points

### Journal Integration
- Entry-to-blog conversion
- Shared block structure
- Content reusability
- Privacy preservation

### Dashboard Integration
- Blog activity widgets
- Quick post creation
- Recent posts display
- Engagement metrics (future)

### AI Services
- Content ideation
- Writing assistance (future)
- SEO optimization (future)
- Content analysis (future)

## Performance Considerations

### Current Implementation
- No pagination for post lists
- Full post loading
- Basic query optimization
- Client-side filtering

### Optimization Opportunities
- Implement pagination
- Add lazy loading
- Cache public profiles
- Optimize block rendering

## Future Enhancements

### Immediate Priorities

1. **Public Access Policies**: Enable anonymous post viewing
2. **Search Functionality**: Activate disabled search
3. **Rich Profiles**: Add bio, links, customization
4. **Engagement Features**: Views, likes, comments
5. **SEO Optimization**: Meta tags, sitemaps

### Advanced Features

1. **Content Management**:
   - Categories and tags
   - Series/collections
   - Scheduling
   - Versioning

2. **Social Features**:
   - Comments with moderation
   - Reactions and bookmarks
   - Share functionality
   - Mention system

3. **Analytics**:
   - View tracking
   - Engagement metrics
   - Audience insights
   - Growth tracking

4. **Monetization**:
   - Premium content
   - Tip jar
   - Subscription options
   - Ad integration

### Technical Improvements

1. **Performance**:
   - Implement caching
   - Add CDN support
   - Optimize images
   - Lazy load content

2. **Developer Experience**:
   - Fix hook inconsistencies
   - Improve type definitions
   - Add error boundaries
   - Enhance testing

## Best Practices

### For Users
- Start with private drafts
- Review visibility before publishing
- Use AI for inspiration, not replacement
- Build audience gradually
- Engage authentically

### For Developers
- Respect privacy settings
- Test visibility logic thoroughly
- Maintain type safety
- Consider performance impact
- Document API changes

## Design Decisions

### Why Block-Based?
- Consistency with journal
- Granular privacy control
- Flexible content structure
- Future extensibility

### Why Dual Format?
- Blocks for structured content
- Markdown for flexibility
- Migration path options
- Developer familiarity

### Why Status Field?
- Simple publishing workflow
- Clear content states
- Future status options
- Easy filtering

## Conclusion

The Blog module successfully bridges personal reflection and public expression. Its architecture supports users in sharing their journey while maintaining control over their privacy. With a solid foundation in place, the module is positioned for growth into a full-featured publishing platform that respects user autonomy while fostering meaningful connections.