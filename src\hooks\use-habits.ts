"use client"

import { useState, useEffect, useMemo } from "react"
import { Habit } from "@/lib/types"
import { supabase } from "@/lib/supabase"
import { calculateHabitStats, HabitStats } from "@/lib/habit-utils"
import { startOfWeek } from "date-fns"

export interface EnhancedHabit extends Habit {
  stats: HabitStats
  isCompletedToday: boolean
}

export function useHabits() {
  const [habits, setHabits] = useState<Habit[]>([])
  const [loading, setLoading] = useState(true)

  const weekStart = useMemo(() => startOfWeek(new Date(), { weekStartsOn: 1 }), [])

  const enhancedHabits = useMemo<EnhancedHabit[]>(() => {
    const today = new Date().toISOString().split('T')[0]
    
    return habits.map(habit => {
      const stats = calculateHabitStats(habit.checkins || [], habit.frequency, weekStart)
      const isCompletedToday = (habit.checkins || []).includes(today)
      
      return {
        ...habit,
        stats,
        isCompletedToday
      }
    })
  }, [habits, weekStart])

  useEffect(() => {
    loadHabits()
  }, [])

  const loadHabits = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('habits')
        .select('id, name, frequency, checkins, created_at')
        .order('created_at', { ascending: false })

      if (error) throw error
      setHabits(data || [])
    } catch (error) {
      console.error('Failed to load habits:', error)
      setHabits([])
    } finally {
      setLoading(false)
    }
  }

  const addHabit = async (name: string, frequency: number) => {
    try {
      const { data, error } = await supabase
        .from('habits')
        .insert({ name, frequency, checkins: [] })
        .select('id, name, frequency, checkins, created_at')
        .single()

      if (error) throw error

      if (data) {
        setHabits((prev) => [data, ...prev])
        
        // Create initial habit event
        await supabase
          .from('habit_events')
          .insert({ 
            habit_id: data.id, 
            event_date: new Date().toISOString().split('T')[0], 
            completed: false 
          })
          .catch(console.error)
      }
    } catch (error) {
      console.error('Failed to add habit:', error)
    }
  }

  const toggleCheckin = async (id: string, date: string = new Date().toISOString().split('T')[0]) => {
    try {
      // Optimistic update
      setHabits((prev) =>
        prev.map((h) => {
          if (h.id !== id) return h
          
          const current = h.checkins || []
          const isCurrentlyChecked = current.includes(date)
          const updatedCheckins = isCurrentlyChecked
            ? current.filter((d: string) => d !== date)
            : [...current, date]
          
          return { ...h, checkins: updatedCheckins }
        })
      )

      // Get current checkins
      const { data: habit } = await supabase
        .from('habits')
        .select('checkins')
        .eq('id', id)
        .single()

      if (!habit) {
        // Rollback optimistic update
        await loadHabits()
        return
      }

      const current = habit.checkins || []
      const isChecked = current.includes(date)
      const updatedCheckins = isChecked
        ? current.filter((d: string) => d !== date)
        : [...current, date]

      // Update database
      const { error } = await supabase
        .from('habits')
        .update({ checkins: updatedCheckins })
        .eq('id', id)

      if (error) {
        console.error('Failed to update habit:', error)
        // Rollback optimistic update
        await loadHabits()
        return
      }

      // Record habit event
      await supabase
        .from('habit_events')
        .insert({ 
          habit_id: id, 
          event_date: date, 
          completed: !isChecked 
        })
        .catch(console.error)

    } catch (error) {
      console.error('Failed to toggle checkin:', error)
      // Rollback optimistic update
      await loadHabits()
    }
  }

  const deleteHabit = async (id: string) => {
    try {
      const { error } = await supabase
        .from('habits')
        .delete()
        .eq('id', id)

      if (error) throw error

      setHabits((prev) => prev.filter(h => h.id !== id))
    } catch (error) {
      console.error('Failed to delete habit:', error)
    }
  }

  return { 
    habits: enhancedHabits, 
    loading,
    addHabit, 
    toggleCheckin,
    deleteHabit,
    refresh: loadHabits
  }
}
