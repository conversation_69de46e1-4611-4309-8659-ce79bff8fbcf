"use client"

import React from 'react'
import { Circle, CircleCheck, MessageSquare, AlertCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

export interface Issue {
  id: string
  number: number
  title: string
  description?: string
  status: 'open' | 'closed'
  priority: 'high' | 'medium' | 'low'
  labels: string[]
  assignee?: {
    name: string
    avatar?: string
  }
  dueDate?: Date
  createdAt: Date
  comments?: number
}

interface IssueCardProps {
  issue: Issue
  onToggle?: () => void
  onClick?: () => void
  className?: string
}

const priorityConfig = {
  high: {
    label: 'Priority: High',
    className: 'bg-red-100 text-red-800 border-red-200'
  },
  medium: {
    label: 'Priority: Medium',
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
  },
  low: {
    label: 'Priority: Low',
    className: 'bg-green-100 text-green-800 border-green-200'
  }
}

export function IssueCard({ issue, onToggle, onClick, className }: IssueCardProps) {
  const isOverdue = issue.dueDate && new Date(issue.dueDate) < new Date() && issue.status === 'open'
  
  return (
    <div 
      className={cn(
        "flex items-start gap-3 p-3 hover:bg-muted/50 transition-colors cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      <button
        className="mt-1 text-muted-foreground hover:text-foreground transition-colors"
        onClick={(e) => {
          e.stopPropagation()
          onToggle?.()
        }}
      >
        {issue.status === 'open' ? (
          <Circle className="w-5 h-5" />
        ) : (
          <CircleCheck className="w-5 h-5 text-purple-600" />
        )}
      </button>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start gap-2 mb-1">
          <h4 className={cn(
            "font-medium hover:text-[#0366d6] transition-colors",
            issue.status === 'closed' && "text-muted-foreground line-through"
          )}>
            {issue.title}
            <span className="text-muted-foreground font-normal ml-1">
              #{issue.number}
            </span>
          </h4>
          
          {isOverdue && (
            <AlertCircle className="w-4 h-4 text-red-600 flex-shrink-0" />
          )}
        </div>
        
        {issue.description && issue.status === 'open' && (
          <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
            {issue.description}
          </p>
        )}
        
        <div className="flex items-center gap-2 flex-wrap">
          <Badge 
            variant="outline" 
            className={cn(
              "text-xs px-2 py-0 h-5",
              priorityConfig[issue.priority].className
            )}
          >
            {priorityConfig[issue.priority].label}
          </Badge>
          
          {issue.labels.map(label => (
            <Badge 
              key={label} 
              variant="outline" 
              className="text-xs px-2 py-0 h-5"
            >
              {label}
            </Badge>
          ))}
          
          {issue.dueDate && (
            <span className={cn(
              "text-xs text-muted-foreground",
              isOverdue && "text-red-600 font-medium"
            )}>
              {isOverdue ? 'Overdue' : 'Due'} {formatDistanceToNow(issue.dueDate, { addSuffix: true })}
            </span>
          )}
          
          {issue.assignee && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <span>Assigned to</span>
              <span className="font-medium">{issue.assignee.name}</span>
            </div>
          )}
          
          {issue.comments && issue.comments > 0 && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <MessageSquare className="w-3 h-3" />
              <span>{issue.comments}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}