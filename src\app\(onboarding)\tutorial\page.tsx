
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { LayoutGrid, MousePointerClick } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function DashboardTutorialPage() {
  return (
    <Card className="w-full shadow-xl">
      <CardHeader className="text-center">
        <LayoutGrid className="h-12 w-12 text-primary mx-auto mb-2" />
        <CardTitle className="text-3xl font-headline">Dashboard Tour</CardTitle>
        <CardDescription className="text-lg">
          Let's quickly walk through your new dashboard.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
          <Image 
            src="https://placehold.co/600x338.png" 
            alt="Dashboard tutorial placeholder" 
            width={600} 
            height={338} 
            className="rounded-md"
            data-ai-hint="dashboard tutorial"
          />
        </div>
        <div className="space-y-2 text-sm text-muted-foreground">
          <p className="flex items-center"><MousePointerClick className="h-4 w-4 mr-2 text-primary" /> <strong>Customizable Blocks:</strong> Arrange your dashboard with widgets like Journal, Habits, and AI Insights.</p>
          <p className="flex items-center"><MousePointerClick className="h-4 w-4 mr-2 text-primary" /> <strong>Quick Add:</strong> Use the floating action button to log entries fast.</p>
          <p className="flex items-center"><MousePointerClick className="h-4 w-4 mr-2 text-primary" /> <strong>Navigation:</strong> Access all features from the sidebar.</p>
        </div>
        <p className="text-sm text-muted-foreground text-center font-semibold">
            You're all set to explore LifeSync AI!
        </p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" asChild>
            <Link href="/onboarding/first-entry">Back</Link>
        </Button>
        <Button asChild size="lg">
          <Link href="/dashboard">Go to Dashboard!</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
