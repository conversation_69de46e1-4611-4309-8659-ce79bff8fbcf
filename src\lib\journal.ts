import { Block } from './types'
import { supabase } from './supabase'

export interface JournalEntry {
  id: string;
  title: string;
  blocks: Block[];
  /**
   * Simple string describing the user's mood for this entry.
   * Examples: "happy", "neutral", "sad".
   */
  mood: string;
  createdAt: string; // ISO string

}


export async function getEntries(): Promise<JournalEntry[]> {
  try {
    const { data } = await supabase
      .from('entries')
      .select('id, title, blocks, mood, created_at')
      .order('created_at', { ascending: false })

    return (data || []).map((e: any) => ({
      id: e.id,
      title: e.title,
      blocks: e.blocks,
      mood: e.mood || '',
      createdAt: e.created_at,
    }))
  } catch {
    return []
  }
}

export async function addEntry(title: string, blocks: Block[], mood: string): Promise<JournalEntry | undefined> {
  const { data } = await supabase
    .from('entries')
    .insert({ title, blocks, mood })
    .select('id, title, blocks, mood, created_at')
    .single()

  if (data) {
    return {
      id: data.id,
      title: data.title,
      blocks: data.blocks,
      mood: data.mood || '',
      createdAt: data.created_at,
    }
  }
  return undefined
}

export async function getEntry(id: string): Promise<JournalEntry | undefined> {
  const { data } = await supabase
    .from('entries')
    .select('id, title, blocks, mood, created_at')
    .eq('id', id)
    .single()

  if (data) {
    return { id: data.id, title: data.title, blocks: data.blocks, mood: data.mood || '', createdAt: data.created_at }
  }
  return undefined
}

export async function updateEntry(id: string, title: string, blocks: Block[], mood: string): Promise<JournalEntry | undefined> {
  const { data } = await supabase
    .from('entries')
    .update({ title, blocks, mood })
    .eq('id', id)
    .select('id, title, blocks, mood, created_at')
    .single()

  if (data) {
    return { id: data.id, title: data.title, blocks: data.blocks, mood: data.mood || '', createdAt: data.created_at }
  }
  return undefined
}


export async function getLatestEntry(): Promise<JournalEntry | undefined> {
  try {
    const { data } = await supabase
      .from('entries')
      .select('id, title, blocks, created_at')
      .order('created_at', { ascending: false })
      .limit(1)

    if (data && data.length > 0) {
      const e = data[0] as any
      return {
        id: e.id,
        title: e.title,
        blocks: e.blocks,
        mood: '',
        createdAt: e.created_at,
      }
    }
  } catch {
    // ignore
  }
  return undefined

}

