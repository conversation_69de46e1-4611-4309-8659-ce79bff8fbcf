'use client';

import { useEffect, useState } from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardFooter, CardContent } from '@/components/ui/card';
import ReflectionForm from './reflection-form';
import { getLatestEntry, JournalEntry } from '@/lib/journal-store';
import { Lightbulb } from 'lucide-react';

export default function MorningCardClient({ prompt }: { prompt: string }) {
  const [latest, setLatest] = useState<JournalEntry | undefined>(undefined);

  useEffect(() => {
    setLatest(getLatestEntry());
  }, []);

  const handleSaved = (entry: JournalEntry) => {
    setLatest(entry);
  };

  return (
    <Card className="w-full shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-headline">
          <Lightbulb className="h-6 w-6 text-primary" />
          Morning Reflection
        </CardTitle>
        <CardDescription className="pt-2 text-base">{prompt}</CardDescription>
      </CardHeader>
      <ReflectionForm onSaved={handleSaved} />
      {latest && (
        <CardFooter className="border-t pt-4">
          <p className="text-sm text-muted-foreground whitespace-pre-wrap">
            {latest.text}
          </p>
        </CardFooter>
      )}
    </Card>
  );
}
