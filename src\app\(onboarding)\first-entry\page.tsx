
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON>, Mi<PERSON> } from "lucide-react";
import Link from "next/link";

export default function FirstEntryPage() {
  return (
    <Card className="w-full shadow-xl">
      <CardHeader className="text-center">
        <BookOpen className="h-12 w-12 text-primary mx-auto mb-2" />
        <CardTitle className="text-3xl font-headline">Your First Journal Entry</CardTitle>
        <CardDescription className="text-lg">
          Let's start by capturing your current thoughts or feelings. No pressure, just write!
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label htmlFor="first-journal-entry" className="block text-sm font-medium text-muted-foreground mb-1">
            Today's thoughts: (e.g., What are you looking forward to?)
          </label>
          <Textarea
            id="first-journal-entry"
            placeholder="Write anything that comes to mind..."
            className="min-h-[150px] text-base"
            aria-label="First journal entry input"
          />
        </div>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <p>Tip: You can use rich text formatting!</p>
          <Button variant="outline" size="sm" disabled>
            <Mic className="mr-2 h-4 w-4" /> Voice Entry (Coming Soon)
          </Button>
        </div>
        <p className="text-xs text-muted-foreground text-center pt-2">
           Your entries are private by default. You can always change privacy settings per entry.
        </p>
      </CardContent>
      <CardFooter className="flex justify-between">
         <Button variant="outline" asChild>
            <Link href="/onboarding/ai-personality">Back</Link>
        </Button>
        <Button asChild size="lg">
          <Link href="/onboarding/tutorial">Next: Dashboard Tour</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
