"use client"

import React from 'react'
import { GitCommit, Star, GitFork, Trophy, Users, Target } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { cn } from '@/lib/utils'

export interface Activity {
  id: string
  type: 'commit' | 'star' | 'fork' | 'achievement' | 'collaboration' | 'milestone'
  user: {
    name: string
    avatar?: string
  }
  habitName?: string
  message: string
  timestamp: Date
  metadata?: {
    count?: number
    streak?: number
    milestone?: string
    collaborator?: string
  }
}

interface ActivityFeedProps {
  activities: Activity[]
  className?: string
}

const activityIcons = {
  commit: GitCommit,
  star: Star,
  fork: GitFork,
  achievement: Trophy,
  collaboration: Users,
  milestone: Target
}

const activityColors = {
  commit: 'text-green-600',
  star: 'text-yellow-600',
  fork: 'text-blue-600',
  achievement: 'text-purple-600',
  collaboration: 'text-pink-600',
  milestone: 'text-orange-600'
}

export function ActivityFeed({ activities, className }: ActivityFeedProps) {
  return (
    <div className={cn("space-y-3", className)}>
      {activities.length === 0 ? (
        <p className="text-center text-muted-foreground py-8">
          No recent activity. Start tracking habits to see your progress!
        </p>
      ) : (
        activities.map(activity => {
          const Icon = activityIcons[activity.type]
          const iconColor = activityColors[activity.type]
          
          return (
            <div key={activity.id} className="flex gap-3">
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center bg-muted",
                iconColor
              )}>
                <Icon className="w-4 h-4" />
              </div>
              
              <div className="flex-1 -mt-0.5">
                <div className="text-sm">
                  <span className="font-medium">{activity.user.name}</span>
                  {' '}
                  {renderActivityMessage(activity)}
                </div>
                
                <div className="text-xs text-muted-foreground mt-0.5">
                  {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                </div>
              </div>
            </div>
          )
        })
      )}
    </div>
  )
}

function renderActivityMessage(activity: Activity): React.ReactNode {
  switch (activity.type) {
    case 'commit':
      return (
        <>
          committed to <span className="font-medium">{activity.habitName}</span>
          {activity.metadata?.count && activity.metadata.count > 1 && (
            <span className="text-muted-foreground"> ({activity.metadata.count} times today)</span>
          )}
        </>
      )
    
    case 'star':
      return (
        <>
          starred <span className="font-medium">{activity.habitName}</span>
        </>
      )
    
    case 'fork':
      return (
        <>
          forked <span className="font-medium">{activity.habitName}</span>
        </>
      )
    
    case 'achievement':
      return (
        <>
          achieved <span className="font-medium text-purple-600">
            {activity.metadata?.streak}-day streak
          </span> on <span className="font-medium">{activity.habitName}</span>
        </>
      )
    
    case 'collaboration':
      return (
        <>
          and <span className="font-medium">{activity.metadata?.collaborator}</span> completed{' '}
          <span className="font-medium">{activity.habitName}</span> together
        </>
      )
    
    case 'milestone':
      return (
        <>
          reached milestone <span className="font-medium text-orange-600">
            {activity.metadata?.milestone}
          </span> on <span className="font-medium">{activity.habitName}</span>
        </>
      )
    
    default:
      return activity.message
  }
}