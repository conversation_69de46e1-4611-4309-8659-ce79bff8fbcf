"use client"

import React from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Trophy, Flame, GitCommit, Calendar, Target, Users } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProfileStats {
  totalCommits: number
  currentStreak: number
  longestStreak: number
  totalHabits: number
  achievements: number
  collaborators: number
}

interface ProfileSidebarProps {
  user: {
    name: string
    username: string
    avatar?: string
    bio?: string
    status?: string
  }
  stats: ProfileStats
  achievements?: Array<{
    id: string
    name: string
    icon: string
    unlockedAt?: Date
  }>
  className?: string
}

export function ProfileSidebar({ user, stats, achievements = [], className }: ProfileSidebarProps) {
  const initials = user.name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)

  return (
    <div className={cn("space-y-4", className)}>
      {/* User Info */}
      <div className="flex flex-col items-center text-center">
        <Avatar className="w-24 h-24 mb-4">
          <AvatarImage src={user.avatar} alt={user.name} />
          <AvatarFallback className="text-2xl">{initials}</AvatarFallback>
        </Avatar>
        
        <h2 className="text-xl font-semibold">{user.name}</h2>
        <p className="text-muted-foreground">@{user.username}</p>
        
        {user.bio && (
          <p className="text-sm mt-2">{user.bio}</p>
        )}
        
        {user.status && (
          <Badge variant="secondary" className="mt-2">
            {user.status}
          </Badge>
        )}
      </div>

      {/* Stats */}
      <Card className="p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <Trophy className="w-4 h-4" />
          Statistics
        </h3>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground flex items-center gap-2">
              <GitCommit className="w-4 h-4" />
              Total Commits
            </span>
            <span className="font-mono font-medium">{stats.totalCommits}</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground flex items-center gap-2">
              <Flame className="w-4 h-4" />
              Current Streak
            </span>
            <span className="font-mono font-medium">{stats.currentStreak} days</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Longest Streak
            </span>
            <span className="font-mono font-medium">{stats.longestStreak} days</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground flex items-center gap-2">
              <Target className="w-4 h-4" />
              Active Habits
            </span>
            <span className="font-mono font-medium">{stats.totalHabits}</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground flex items-center gap-2">
              <Trophy className="w-4 h-4" />
              Achievements
            </span>
            <span className="font-mono font-medium">{stats.achievements}</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground flex items-center gap-2">
              <Users className="w-4 h-4" />
              Collaborators
            </span>
            <span className="font-mono font-medium">{stats.collaborators}</span>
          </div>
        </div>
      </Card>

      {/* Achievements */}
      {achievements.length > 0 && (
        <Card className="p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Trophy className="w-4 h-4" />
            Recent Achievements
          </h3>
          
          <div className="grid grid-cols-3 gap-2">
            {achievements.slice(0, 6).map(achievement => (
              <div
                key={achievement.id}
                className="flex flex-col items-center gap-1 p-2 rounded hover:bg-muted transition-colors cursor-pointer"
                title={achievement.name}
              >
                <span className="text-2xl">{achievement.icon}</span>
                <span className="text-xs text-center line-clamp-1">
                  {achievement.name}
                </span>
              </div>
            ))}
          </div>
          
          {achievements.length > 6 && (
            <Button variant="ghost" size="sm" className="w-full mt-2">
              View all achievements
            </Button>
          )}
        </Card>
      )}

      {/* Action Buttons */}
      <div className="space-y-2">
        <Button className="w-full" variant="outline">
          <Users className="w-4 h-4 mr-2" />
          Find Accountability Partners
        </Button>
      </div>
    </div>
  )
}