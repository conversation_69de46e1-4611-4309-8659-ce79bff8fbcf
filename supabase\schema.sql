-- Tables
create table if not exists public.entries (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  title text not null,
  blocks jsonb not null,
  mood text,
  created_at timestamp with time zone default now()
);

create table if not exists public.habits (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  name text not null,
  frequency integer not null,
  checkins date[] default '{}',
  created_at timestamp with time zone default now()
);

create table if not exists public.moods (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  rating integer not null,
  note text,
  created_at timestamp with time zone default now()
);

create table if not exists public.blog_posts (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  entry_id uuid references entries,
  title text not null,
  blocks jsonb not null,
  body_md text,
  status text not null default 'draft',
  created_at timestamp with time zone default now()
);

create table if not exists public.weekly_summaries (
  id uuid primary key default gen_random_uuid(),
  summary text not null,
  created_at timestamp with time zone default now()
);

create table if not exists public.followers (
  follower_id uuid references auth.users not null,
  followed_id uuid references auth.users not null,
  timestamp timestamp with time zone default now(),
  primary key (follower_id, followed_id)
);

create table if not exists public.dashboard_layouts (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null unique,
  blocks jsonb not null,
  public_blocks jsonb,
  updated_at timestamp with time zone default now()
);

create table if not exists public.habit_events (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  habit_id uuid references habits not null,
  event_date date not null,
  completed boolean not null,
  created_at timestamp with time zone default now()
);
