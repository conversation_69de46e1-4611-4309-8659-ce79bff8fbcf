'use client'

import { ReactNode } from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { GripVertical, X } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface BlockContainerProps {
  id: string
  children: ReactNode
  onRemove?: (id: string) => void
}

export default function BlockContainer({ id, children, onRemove }: BlockContainerProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div ref={setNodeRef} style={style} className="relative">
      <div className="absolute right-2 top-2 flex gap-2">
        <Button size="icon" variant="ghost" {...listeners} {...attributes} aria-label="Drag block">
          <GripVertical className="h-4 w-4" />
        </Button>
        {onRemove && (
          <Button size="icon" variant="ghost" onClick={() => onRemove(id)} aria-label="Remove block">
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      {children}
    </div>
  )
}
