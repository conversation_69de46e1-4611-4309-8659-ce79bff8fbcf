
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import Link from "next/link";
import Image from "next/image";

export default function WelcomePage() {
  return (
    <Card className="w-full shadow-xl">
      <CardHeader className="text-center">
        <CardTitle className="text-3xl font-headline">Welcome to LifeSync AI!</CardTitle>
        <CardDescription className="text-lg">
          We're excited to help you organize your life and gain valuable insights.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
          <Image 
            src="https://placehold.co/600x338.png" 
            alt="Platform overview placeholder" 
            width={600} 
            height={338} 
            className="rounded-md"
            data-ai-hint="welcome animation" 
          />
        </div>
        <div className="text-center space-y-2">
          <p className="font-semibold text-foreground">Key Benefits:</p>
          <ul className="list-disc list-inside text-muted-foreground text-sm">
            <li>Unified journaling and habit tracking.</li>
            <li>Personalized AI-driven insights.</li>
            <li>Privacy-first design.</li>
          </ul>
        </div>
        <p className="text-sm text-muted-foreground text-center">
          We are committed to your privacy. Your data is yours, always.
        </p>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button asChild size="lg">
          <Link href="/onboarding/privacy">Get Started</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
