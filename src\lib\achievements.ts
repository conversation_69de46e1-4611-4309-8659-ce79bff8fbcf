import { HabitStreak, HabitStats } from './habit-utils'

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'streak' | 'completion' | 'consistency' | 'milestone' | 'special'
  criteria: (stats: HabitStats) => boolean
  unlockedAt?: Date
}

export const ACHIEVEMENTS: Achievement[] = [
  // Streak Achievements
  {
    id: 'first-commit',
    name: 'First Commit',
    description: 'Complete your first habit',
    icon: '🎯',
    category: 'milestone',
    criteria: (stats) => stats.totalCompletions >= 1
  },
  {
    id: 'week-warrior',
    name: 'Week Warrior',
    description: 'Maintain a 7-day streak',
    icon: '📅',
    category: 'streak',
    criteria: (stats) => stats.streak.current >= 7 || stats.streak.longest >= 7
  },
  {
    id: 'habit-hero',
    name: 'Habit Hero',
    description: 'Maintain a 30-day streak',
    icon: '🦸',
    category: 'streak',
    criteria: (stats) => stats.streak.current >= 30 || stats.streak.longest >= 30
  },
  {
    id: 'streak-master',
    name: 'Streak Master',
    description: 'Maintain a 100-day streak',
    icon: '🔥',
    category: 'streak',
    criteria: (stats) => stats.streak.current >= 100 || stats.streak.longest >= 100
  },
  {
    id: 'year-long-dedication',
    name: 'Year-long Dedication',
    description: 'Maintain a 365-day streak',
    icon: '🏆',
    category: 'streak',
    criteria: (stats) => stats.streak.current >= 365 || stats.streak.longest >= 365
  },
  
  // Completion Achievements
  {
    id: 'centurion',
    name: 'Centurion',
    description: 'Complete 100 total check-ins',
    icon: '💯',
    category: 'completion',
    criteria: (stats) => stats.totalCompletions >= 100
  },
  {
    id: 'thousand-commits',
    name: 'Thousand Commits',
    description: 'Complete 1000 total check-ins',
    icon: '🎊',
    category: 'completion',
    criteria: (stats) => stats.totalCompletions >= 1000
  },
  
  // Consistency Achievements
  {
    id: 'perfect-week',
    name: 'Perfect Week',
    description: 'Complete all habits for a full week',
    icon: '⭐',
    category: 'consistency',
    criteria: (stats) => stats.weeklyCompletionRate >= 100 && stats.currentWeekCompletions >= stats.targetWeeklyCompletions
  },
  {
    id: 'monthly-consistency',
    name: 'Monthly Consistency',
    description: 'Maintain 80% completion rate for a month',
    icon: '🌟',
    category: 'consistency',
    criteria: (stats) => stats.monthlyCompletionRate >= 80
  },
  {
    id: 'comeback-kid',
    name: 'Comeback Kid',
    description: 'Restart after breaking a 7+ day streak',
    icon: '💪',
    category: 'special',
    criteria: (stats) => stats.streak.current >= 3 && stats.streak.breaks > 0
  },
  
  // Milestone Achievements
  {
    id: 'early-bird',
    name: 'Early Bird',
    description: 'Complete habits before 8 AM',
    icon: '🌅',
    category: 'special',
    criteria: (stats) => false // This would need time-based data
  },
  {
    id: 'night-owl',
    name: 'Night Owl',
    description: 'Complete habits after 10 PM',
    icon: '🦉',
    category: 'special',
    criteria: (stats) => false // This would need time-based data
  },
]

export function calculateAchievements(stats: HabitStats): Achievement[] {
  return ACHIEVEMENTS.filter(achievement => achievement.criteria(stats))
}

export function getAchievementById(id: string): Achievement | undefined {
  return ACHIEVEMENTS.find(achievement => achievement.id === id)
}

export function getAchievementProgress(achievement: Achievement, stats: HabitStats): number {
  switch (achievement.id) {
    case 'first-commit':
      return Math.min(stats.totalCompletions, 1)
    case 'week-warrior':
      return Math.min((Math.max(stats.streak.current, stats.streak.longest) / 7) * 100, 100)
    case 'habit-hero':
      return Math.min((Math.max(stats.streak.current, stats.streak.longest) / 30) * 100, 100)
    case 'streak-master':
      return Math.min((Math.max(stats.streak.current, stats.streak.longest) / 100) * 100, 100)
    case 'year-long-dedication':
      return Math.min((Math.max(stats.streak.current, stats.streak.longest) / 365) * 100, 100)
    case 'centurion':
      return Math.min((stats.totalCompletions / 100) * 100, 100)
    case 'thousand-commits':
      return Math.min((stats.totalCompletions / 1000) * 100, 100)
    default:
      return achievement.criteria(stats) ? 100 : 0
  }
}

export function getNextAchievements(stats: HabitStats, limit: number = 3): Achievement[] {
  const unlockedIds = calculateAchievements(stats).map(a => a.id)
  
  return ACHIEVEMENTS
    .filter(achievement => !unlockedIds.includes(achievement.id))
    .map(achievement => ({
      ...achievement,
      progress: getAchievementProgress(achievement, stats)
    }))
    .sort((a, b) => {
      // Sort by progress (closest to completion first)
      const progressA = getAchievementProgress(a, stats)
      const progressB = getAchievementProgress(b, stats)
      return progressB - progressA
    })
    .slice(0, limit)
}