'use client';

import { useEffect, useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { getLatestEntry, JournalEntry } from '@/lib/journal';

export default function LatestReflectionCard() {
  const [entry, setEntry] = useState<JournalEntry | undefined>(undefined);

  useEffect(() => {
    getLatestEntry().then(setEntry).catch(() => {});
  }, []);

  if (!entry) {
    return null;
  }

  return (
    <Card className="mb-6 shadow-lg">
      <CardHeader>
        <CardTitle className="font-headline">Latest Reflection</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="whitespace-pre-wrap text-muted-foreground">
          {entry.blocks.map(b => b.text).join('\n\n')}
        </p>
      </CardContent>
    </Card>
  );
}
