
'use client'

import type { PropsWithChildren } from 'react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import Link from 'next/link'
import { BrainCircuit } from 'lucide-react'
import { useOnboardingProgress } from '@/hooks/use-onboarding-progress'

export default function OnboardingLayout({ children }: PropsWithChildren) {
  const { currentStep, totalSteps } = useOnboardingProgress()
  const progressValue = (currentStep / totalSteps) * 100

  return (
    <div className="flex min-h-screen flex-col items-center bg-background p-4 sm:p-8">
      <header className="w-full max-w-3xl mb-8">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center gap-2 text-xl font-headline font-semibold text-primary">
            <BrainCircuit className="h-7 w-7" />
            LifeSync AI
          </Link>
          <div className="text-sm text-muted-foreground">
            Step {currentStep} of {totalSteps}
          </div>
        </div>
        <Progress value={progressValue} className="mt-2 h-2" />
      </header>
      <main className="w-full max-w-3xl flex-grow">
        {children}
      </main>
      <footer className="w-full max-w-3xl mt-8 pt-4 border-t">
        <div className="flex justify-between items-center">
          <Button variant="outline" asChild>
            <Link href="/dashboard">Skip for now</Link>
          </Button>
        </div>
      </footer>
    </div>
  );
}
