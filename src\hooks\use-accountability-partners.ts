"use client"

import { useState, useEffect, useCallback } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { AccountabilityPartner } from '@/components/github/AccountabilityPartners'
import { useAuth } from '@/components/auth-provider'

export function useAccountabilityPartners() {
  const { user } = useAuth()
  const [partners, setPartners] = useState<AccountabilityPartner[]>([])
  const [pendingRequests, setPendingRequests] = useState<AccountabilityPartner[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient()

  const fetchPartners = useCallback(async () => {
    if (!user) return

    try {
      // Fetch accepted partnerships
      const { data: acceptedData, error: acceptedError } = await supabase
        .from('accountability_partners')
        .select('*')
        .or(`requester_id.eq.${user.id},partner_id.eq.${user.id}`)
        .eq('status', 'accepted')

      if (acceptedError) throw acceptedError

      // Fetch pending requests
      const { data: pendingData, error: pendingError } = await supabase
        .from('accountability_partners')
        .select('*')
        .eq('partner_id', user.id)
        .eq('status', 'pending')

      if (pendingError) throw pendingError

      // Format partners data
      const formattedPartners = acceptedData?.map(p => ({
        id: p.id,
        partnerId: p.requester_id === user.id ? p.partner_id : p.requester_id,
        partnerEmail: '', // Would need to join with auth.users
        status: p.status,
        createdAt: new Date(p.created_at),
      })) || []

      const formattedPending = pendingData?.map(p => ({
        id: p.id,
        partnerId: p.requester_id,
        partnerEmail: '', // Would need to join with auth.users
        status: p.status,
        createdAt: new Date(p.created_at),
      })) || []

      setPartners(formattedPartners)
      setPendingRequests(formattedPending)
    } catch (error) {
      console.error('Error fetching partners:', error)
    } finally {
      setLoading(false)
    }
  }, [user, supabase])

  useEffect(() => {
    fetchPartners()
  }, [fetchPartners])

  const invitePartner = async (email: string, message?: string) => {
    if (!user) throw new Error('User not authenticated')

    // Find user by email
    const { data: userData, error: userError } = await supabase
      .rpc('get_user_by_email', { email })

    if (userError || !userData) {
      throw new Error('User not found')
    }

    // Create partnership request
    const { error } = await supabase
      .from('accountability_partners')
      .insert({
        requester_id: user.id,
        partner_id: userData.id,
        status: 'pending',
        message,
      })

    if (error) throw error

    await fetchPartners()
  }

  const acceptRequest = async (requestId: string) => {
    const { error } = await supabase
      .from('accountability_partners')
      .update({ status: 'accepted', updated_at: new Date().toISOString() })
      .eq('id', requestId)

    if (error) throw error

    await fetchPartners()
  }

  const declineRequest = async (requestId: string) => {
    const { error } = await supabase
      .from('accountability_partners')
      .update({ status: 'declined', updated_at: new Date().toISOString() })
      .eq('id', requestId)

    if (error) throw error

    await fetchPartners()
  }

  const removePartner = async (partnerId: string) => {
    const { error } = await supabase
      .from('accountability_partners')
      .update({ status: 'removed', updated_at: new Date().toISOString() })
      .eq('id', partnerId)

    if (error) throw error

    await fetchPartners()
  }

  return {
    partners,
    pendingRequests,
    loading,
    invitePartner,
    acceptRequest,
    declineRequest,
    removePartner,
    refetch: fetchPartners,
  }
}