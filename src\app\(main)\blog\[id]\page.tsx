"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { getPost } from "@/lib/blog";

export default function ViewBlogPostPage() {
  const params = useParams<{ id: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);

  useEffect(() => {
    getPost(params.id).then((existing) => {
      if (existing) setPost(existing);
    });
  }, [params.id]);

  if (!post) {
    return (
      <div className="container mx-auto py-2">
        <p className="text-muted-foreground">Post not found.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-2">
      <Link href="/blog" className="inline-flex items-center text-sm text-primary hover:underline mb-4">
        <ChevronLeft className="mr-1 h-4 w-4" />
        Back to Blog
      </Link>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="font-headline text-2xl">{post.title || "Untitled Post"}</CardTitle>
          <CardDescription className="capitalize">{post.status}</CardDescription>
        </CardHeader>
        <CardContent className="prose max-w-none">
          {post.blocks
            .filter((b) => b.visibility === "public")
            .map((block, i) => (
              <p key={i}>{block.text}</p>
            ))}
        </CardContent>
      </Card>
    </div>
  );
}
