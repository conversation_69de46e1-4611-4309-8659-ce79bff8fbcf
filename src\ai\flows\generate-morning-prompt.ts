
'use server';

/**
 * @fileOverview A flow to generate a unique morning reflection prompt.
 *
 * - generateMorningPrompt - A function that generates the morning prompt.
 * - GenerateMorningPromptInput - The input type for the generateMorningPrompt function (void).
 * - GenerateMorningPromptOutput - The return type for the generateMorningPrompt function (string).
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateMorningPromptInputSchema = z.void();
export type GenerateMorningPromptInput = z.infer<typeof GenerateMorningPromptInputSchema>;

const GenerateMorningPromptOutputSchema = z.string().describe('A unique morning reflection prompt, consisting of exactly two sentences.');
export type GenerateMorningPromptOutput = z.infer<typeof GenerateMorningPromptOutputSchema>;

export async function generateMorningPrompt(): Promise<GenerateMorningPromptOutput> {
  return generateMorningPromptFlow();
}

const prompt = ai.definePrompt({
  name: 'generateMorningPromptPrompt',
  input: {schema: GenerateMorningPromptInputSchema},
  output: {schema: GenerateMorningPromptOutputSchema},
  prompt: `Generate a unique and thought-provoking 2-sentence morning reflection prompt.
It should be genuinely motivational, avoiding clichés and cheesiness. The prompt should encourage introspection on one of the following themes:
- A specific aspect of gratitude (e.g., "What small, overlooked thing brought you joy yesterday, and how can you carry that feeling forward?").
- Identifying a single, impactful priority for the day (e.g., "If you could only accomplish one meaningful thing today, what would it be and why is it important right now?").
- A constructive way to approach your current mood (e.g., "Whatever your mood, what's one action you can take to either amplify positivity or gently shift a less positive state?").
- A connection between a past learning and a present opportunity (e.g., "Reflect on a past challenge you overcame. What strength did you discover then that you can apply to today's plans?").
- A moment of mindfulness or presence (e.g., "What's one thing you can do today to be fully present in an ordinary moment? How might that enrich your day?").
Ensure the prompt is concise, consisting of exactly two sentences, and directly actionable in thought. Vary the theme of the prompt.`,
});

const generateMorningPromptFlow = ai.defineFlow(
  {
    name: 'generateMorningPromptFlow',
    inputSchema: GenerateMorningPromptInputSchema,
    outputSchema: GenerateMorningPromptOutputSchema,
  },
  async () => {
    const {output} = await prompt({});
    return output!;
  }
);
