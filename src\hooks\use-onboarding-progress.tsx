'use client'

import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'

const STEP_SEGMENTS = ['welcome', 'privacy', 'ai-personality', 'first-entry', 'tutorial']
const STORAGE_KEY = 'onboardingStep'

export function useOnboardingProgress() {
  const pathname = usePathname()
  const [currentStep, setCurrentStep] = useState(1)
  const totalSteps = STEP_SEGMENTS.length

  useEffect(() => {
    // Initialize from storage on first load
    const saved = parseInt(localStorage.getItem(STORAGE_KEY) || '1', 10)
    setCurrentStep(saved)
  }, [])

  useEffect(() => {
    const segment = pathname.split('/').pop() ?? ''
    const index = STEP_SEGMENTS.indexOf(segment)
    if (index >= 0) {
      const stepNumber = index + 1
      setCurrentStep(stepNumber)
      localStorage.setItem(STORAGE_KEY, stepNumber.toString())
    }
  }, [pathname])

  return { currentStep, totalSteps }
}
