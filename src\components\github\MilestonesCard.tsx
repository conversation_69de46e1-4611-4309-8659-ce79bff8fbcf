"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Target, Plus, Calendar as CalendarIcon, Trophy, Flag } from 'lucide-react'
import { format, formatDistanceToNow, isPast } from 'date-fns'
import { cn } from '@/lib/utils'

export interface Milestone {
  id: string
  habitId: string
  title: string
  description?: string
  targetValue: number
  currentValue: number
  targetDate?: Date
  completedAt?: Date
  status: 'active' | 'completed' | 'overdue'
}

interface MilestonesCardProps {
  habitId: string
  habitName: string
  milestones: Milestone[]
  currentStreak: number
  totalCompletions: number
  onCreateMilestone: (milestone: Omit<Milestone, 'id' | 'status' | 'currentValue'>) => Promise<void>
  onUpdateMilestone: (id: string, updates: Partial<Milestone>) => Promise<void>
  onDeleteMilestone: (id: string) => Promise<void>
}

export function MilestonesCard({
  habitId,
  habitName,
  milestones,
  currentStreak,
  totalCompletions,
  onCreateMilestone,
  onUpdateMilestone,
  onDeleteMilestone
}: MilestonesCardProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [newMilestone, setNewMilestone] = useState({
    title: '',
    description: '',
    targetValue: 30,
    targetDate: undefined as Date | undefined,
  })

  const handleCreate = async () => {
    if (!newMilestone.title) return

    await onCreateMilestone({
      habitId,
      title: newMilestone.title,
      description: newMilestone.description,
      targetValue: newMilestone.targetValue,
      targetDate: newMilestone.targetDate,
    })

    setShowCreateDialog(false)
    setNewMilestone({
      title: '',
      description: '',
      targetValue: 30,
      targetDate: undefined,
    })
  }

  // Update milestones with current progress
  const enhancedMilestones = milestones.map(milestone => {
    let currentValue = 0
    
    // Determine current value based on milestone type
    if (milestone.title.toLowerCase().includes('streak')) {
      currentValue = currentStreak
    } else if (milestone.title.toLowerCase().includes('total') || milestone.title.toLowerCase().includes('completion')) {
      currentValue = totalCompletions
    } else {
      currentValue = milestone.currentValue
    }

    const progress = Math.min((currentValue / milestone.targetValue) * 100, 100)
    const isOverdue = milestone.targetDate && isPast(milestone.targetDate) && progress < 100

    return {
      ...milestone,
      currentValue,
      progress,
      status: progress >= 100 ? 'completed' : isOverdue ? 'overdue' : 'active' as const
    }
  })

  const activeMilestones = enhancedMilestones.filter(m => m.status === 'active')
  const completedMilestones = enhancedMilestones.filter(m => m.status === 'completed')
  const overdueMilestones = enhancedMilestones.filter(m => m.status === 'overdue')

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Milestones
            </CardTitle>
            <CardDescription>
              Set goals and track your progress for {habitName}
            </CardDescription>
          </div>
          <Button size="sm" onClick={() => setShowCreateDialog(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Milestone
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Active Milestones */}
          {activeMilestones.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-3">Active</h4>
              <div className="space-y-3">
                {activeMilestones.map(milestone => (
                  <MilestoneItem
                    key={milestone.id}
                    milestone={milestone}
                    onUpdate={onUpdateMilestone}
                    onDelete={onDeleteMilestone}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Overdue Milestones */}
          {overdueMilestones.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-3 text-red-600">Overdue</h4>
              <div className="space-y-3">
                {overdueMilestones.map(milestone => (
                  <MilestoneItem
                    key={milestone.id}
                    milestone={milestone}
                    onUpdate={onUpdateMilestone}
                    onDelete={onDeleteMilestone}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Completed Milestones */}
          {completedMilestones.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-3 text-muted-foreground">Completed</h4>
              <div className="space-y-3">
                {completedMilestones.slice(0, 3).map(milestone => (
                  <MilestoneItem
                    key={milestone.id}
                    milestone={milestone}
                    onUpdate={onUpdateMilestone}
                    onDelete={onDeleteMilestone}
                  />
                ))}
              </div>
            </div>
          )}

          {enhancedMilestones.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Target className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No milestones set</p>
              <p className="text-sm mt-2">
                Create milestones to track your long-term goals
              </p>
            </div>
          )}
        </div>
      </CardContent>

      {/* Create Milestone Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Milestone</DialogTitle>
            <DialogDescription>
              Set a goal for your {habitName} habit
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Milestone Name</Label>
              <Input
                id="title"
                placeholder="30-day streak"
                value={newMilestone.title}
                onChange={(e) => setNewMilestone({ ...newMilestone, title: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                placeholder="Complete the habit for 30 consecutive days"
                value={newMilestone.description}
                onChange={(e) => setNewMilestone({ ...newMilestone, description: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="target">Target Value</Label>
              <Input
                id="target"
                type="number"
                min="1"
                value={newMilestone.targetValue}
                onChange={(e) => setNewMilestone({ ...newMilestone, targetValue: parseInt(e.target.value) })}
              />
            </div>
            <div>
              <Label>Target Date (optional)</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !newMilestone.targetDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {newMilestone.targetDate ? format(newMilestone.targetDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={newMilestone.targetDate}
                    onSelect={(date) => setNewMilestone({ ...newMilestone, targetDate: date })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreate} disabled={!newMilestone.title}>
              Create Milestone
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

function MilestoneItem({
  milestone,
  onUpdate,
  onDelete
}: {
  milestone: Milestone & { progress: number }
  onUpdate: (id: string, updates: Partial<Milestone>) => Promise<void>
  onDelete: (id: string) => Promise<void>
}) {
  const statusConfig = {
    active: { icon: Flag, color: 'text-blue-600' },
    completed: { icon: Trophy, color: 'text-green-600' },
    overdue: { icon: Target, color: 'text-red-600' }
  }

  const config = statusConfig[milestone.status]
  const Icon = config.icon

  return (
    <div className="p-3 rounded-lg border hover:bg-muted/50 transition-colors">
      <div className="flex items-start gap-3">
        <div className={cn("mt-0.5", config.color)}>
          <Icon className="w-5 h-5" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h5 className={cn(
              "font-medium",
              milestone.status === 'completed' && "text-muted-foreground line-through"
            )}>
              {milestone.title}
            </h5>
            <Badge
              variant={milestone.status === 'completed' ? 'secondary' : milestone.status === 'overdue' ? 'destructive' : 'default'}
              className="text-xs"
            >
              {milestone.status}
            </Badge>
          </div>
          
          {milestone.description && (
            <p className="text-sm text-muted-foreground mb-2">
              {milestone.description}
            </p>
          )}

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">
                {milestone.currentValue} / {milestone.targetValue}
              </span>
              <span className="font-medium">
                {Math.round(milestone.progress)}%
              </span>
            </div>
            <Progress value={milestone.progress} className="h-2" />
          </div>

          {milestone.targetDate && (
            <p className="text-xs text-muted-foreground mt-2">
              {milestone.status === 'overdue' ? 'Was due' : 'Due'} {formatDistanceToNow(milestone.targetDate, { addSuffix: true })}
            </p>
          )}
        </div>
      </div>
    </div>
  )
}