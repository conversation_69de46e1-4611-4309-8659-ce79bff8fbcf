import { readStorage, writeStorage } from './journal'

export interface JournalEntry {
  id: number;
  text: string;
  createdAt: string; // ISO string for easier serialization
}

const STORAGE_KEY = "journalStoreEntries";

function getStoredEntries(): JournalEntry[] {
  return readStorage<JournalEntry>(STORAGE_KEY);
}

function saveEntries(entries: JournalEntry[]) {
  writeStorage(STORAGE_KEY, entries);
}

export function addJournalEntry(text: string): JournalEntry {
  const entry: JournalEntry = {
    id: Date.now(),
    text,
    createdAt: new Date().toISOString(),
  };
  const entries = getStoredEntries();
  entries.unshift(entry);
  saveEntries(entries);
  return entry;
}

export function getLatestEntry(): JournalEntry | undefined {
  const entries = getStoredEntries();
  return entries[0];
}

export function getAllEntries(): JournalEntry[] {
  return getStoredEntries();
}
