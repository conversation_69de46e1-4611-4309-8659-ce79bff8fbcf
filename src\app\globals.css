@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Inter', sans-serif; /* Ensure Inter is default body font */
}

@layer base {
  :root {
    /* LifeSync AI Theme - Light Mode */
    --background: 216 33% 97%; /* Light gray #F5F7FA */
    --foreground: 210 10% 23%; /* Dark Slate Gray - good contrast */
    --card: 0 0% 100%; /* White */
    --card-foreground: 210 10% 23%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 10% 23%;
    --primary: 220 80% 64%; /* Desaturated blue #5A8DEE */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 210 30% 90%; /* Lighter muted blue-gray */
    --secondary-foreground: 210 10% 15%; /* Darker Gray */
    --muted: 210 30% 85%; /* Muted blue-gray */
    --muted-foreground: 210 10% 45%; /* Medium Gray */
    --accent: 162 71% 45%; /* Soft teal #20C997 */
    --accent-foreground: 0 0% 100%; /* White for contrast on teal */
    --destructive: 0 72% 51%; /* Red */
    --destructive-foreground: 0 0% 98%;
    --border: 210 20% 88%; /* Lighter Border */
    --input: 210 30% 94%; /* Light Input Background */
    --ring: 220 80% 64%; /* Use primary for ring */
    --chart-1: 220 70% 70%; 
    --chart-2: 162 60% 55%;
    --chart-3: 210 50% 60%;
    --chart-4: 190 65% 50%;
    --chart-5: 200 75% 55%;
    --radius: 0.5rem;

    /* Sidebar specific theme variables */
    --sidebar-background: 210 30% 92%; 
    --sidebar-foreground: 210 10% 28%; 
    --sidebar-primary: 220 80% 64%; 
    --sidebar-primary-foreground: 0 0% 100%; 
    --sidebar-accent: 162 71% 45%; 
    --sidebar-accent-foreground: 0 0% 100%; 
    --sidebar-border: 210 20% 85%; 
    --sidebar-ring: 220 80% 64%; 
  }

  .dark {
    /* LifeSync AI Theme - Dark Mode */
    --background: 220 18% 12%; 
    --foreground: 0 0% 98%; 
    --card: 220 15% 18%; 
    --card-foreground: 0 0% 98%;
    --popover: 220 15% 18%;
    --popover-foreground: 0 0% 98%;
    --primary: 220 75% 70%; /* Slightly brighter blue for dark mode */
    --primary-foreground: 0 0% 100%; 
    --secondary: 220 15% 28%; 
    --secondary-foreground: 0 0% 98%;
    --muted: 220 15% 24%; 
    --muted-foreground: 0 0% 65%;
    --accent: 162 65% 55%; /* Slightly brighter teal for dark mode */
    --accent-foreground: 0 0% 10%; 
    --destructive: 0 60% 45%; 
    --destructive-foreground: 0 0% 98%;
    --border: 220 15% 30%; 
    --input: 220 15% 28%; 
    --ring: 220 75% 70%; 
    --chart-1: 220 65% 75%;
    --chart-2: 162 55% 65%;
    --chart-3: 210 45% 70%;
    --chart-4: 190 60% 60%;
    --chart-5: 200 70% 65%;

    --sidebar-background: 220 15% 15%; 
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 220 75% 70%; 
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 162 65% 55%; 
    --sidebar-accent-foreground: 0 0% 10%;
    --sidebar-border: 220 15% 25%;
    --sidebar-ring: 220 75% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-headline;
  }
}
