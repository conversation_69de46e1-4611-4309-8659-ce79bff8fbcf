"use client"

import { useState } from "react"
import { <PERSON><PERSON>, Di<PERSON><PERSON>rigger, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { PlusCircle } from "lucide-react"
import { useHabits } from "@/hooks/use-habits"

export function AddHabitDialog() {
  const { addHabit } = useHabits()
  const [open, setOpen] = useState(false)
  const [name, setName] = useState("")
  const [frequency, setFrequency] = useState("7")

  const submit = () => {
    if (!name.trim()) return
    addHabit(name.trim(), Number(frequency))
    setName("")
    setFrequency("7")
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" /> Add New Habit
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Habit</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Habit name"
          />
          <Select value={frequency} onValueChange={setFrequency}>
            <SelectTrigger>
              <SelectValue placeholder="Times per week" />
            </SelectTrigger>
            <SelectContent>
              {[1,2,3,4,5,6,7].map((n) => (
                <SelectItem key={n} value={String(n)}>
                  {n} time{n > 1 ? "s" : ""} per week
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <DialogFooter>
          <Button onClick={submit}>Save Habit</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
