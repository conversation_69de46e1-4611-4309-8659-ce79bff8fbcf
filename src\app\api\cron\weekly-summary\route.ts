import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateWeeklySummary } from '@/ai/flows/generate-weekly-summary';
import { startOfWeek, isAfter, parseISO } from 'date-fns';
import { createErrorResponse } from '@/lib/auth-server';

export async function GET(request: Request) {
  try {
    // Verify cron secret (you should set this in your environment)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Create service client for cron job
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!serviceRoleKey) {
      console.error('SUPABASE_SERVICE_ROLE_KEY not configured');
      return createErrorResponse('Service not configured', 500);
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get all users who have entries this week
    const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
    
    const { data: users, error: usersError } = await supabase
      .from('entries')
      .select('user_id')
      .gte('created_at', weekStart.toISOString())
      .order('user_id');

    if (usersError) {
      console.error('Failed to fetch users', usersError);
      throw new Error('Failed to fetch users');
    }

    // Get unique user IDs
    const uniqueUserIds = [...new Set(users?.map(u => u.user_id) || [])];
    const summaries = [];

    // Generate summary for each user
    for (const userId of uniqueUserIds) {
      const { data, error } = await supabase
        .from('entries')
        .select('blocks, created_at')
        .eq('user_id', userId)
        .gte('created_at', weekStart.toISOString())
        .order('created_at', { ascending: true });

      if (error) {
        console.error(`Failed to fetch entries for user ${userId}`, error);
        continue;
      }

      const entries = (data || [])
        .map(e => Array.isArray(e.blocks)
          ? (e.blocks as { text: string }[]).map(b => b.text).join('\n')
          : '')
        .filter(text => text.length > 0);

      if (entries.length > 0) {
        try {
          const summary = await generateWeeklySummary({ entries });
          
          // Store summary with user association
          const { error: insertError } = await supabase
            .from('weekly_summaries')
            .insert({ 
              summary, 
              user_id: userId,
              week_start: weekStart.toISOString()
            });

          if (insertError) {
            console.error(`Failed to store summary for user ${userId}`, insertError);
          } else {
            summaries.push({ userId, summary });
          }
        } catch (err) {
          console.error(`Failed to generate summary for user ${userId}`, err);
        }
      }
    }

    return NextResponse.json({ 
      success: true,
      usersProcessed: uniqueUserIds.length,
      summariesGenerated: summaries.length 
    });
  } catch (error) {
    console.error('Cron job failed', error);
    return createErrorResponse('Internal server error', 500);
  }
}
