# Database Design Technical Documentation

## Overview

LifeSync uses Supabase (PostgreSQL) as its primary database, implementing a well-structured schema that balances flexibility with performance. The design emphasizes user data isolation, efficient querying, and future scalability.

## Schema Architecture

### Design Principles

1. **User-Centric**: All primary tables reference auth.users
2. **Flexible Content**: JSONB for evolving data structures
3. **Audit Trail**: Separate tables for historical tracking
4. **Security First**: Row Level Security on all tables
5. **Referential Integrity**: Foreign key constraints throughout

## Table Structures

### Core Tables

#### 1. **entries** (Journal Entries)

```sql
create table public.entries (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  title text,
  blocks jsonb not null,
  mood text,
  created_at timestamp with time zone default now()
);
```

**Design Decisions**:
- **JSONB for blocks**: Allows flexible content structure
- **Optional title**: Not all entries need titles
- **Mood as text**: Flexible for future mood options
- **UUID primary keys**: Globally unique, no collisions

**Indexes**:
- Primary key index on id
- Implicit index on user_id (foreign key)
- Consider adding: created_at DESC for chronological queries

#### 2. **habits**

```sql
create table public.habits (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  name text not null,
  frequency integer not null,
  checkins date[] default '{}',
  created_at timestamp with time zone default now()
);
```

**Design Decisions**:
- **Array for checkins**: Simple, efficient for small datasets
- **Frequency as integer**: Weekly target (1-7)
- **Date array type**: PostgreSQL native array support

**Considerations**:
- Array storage limits scalability
- No native indexing on array elements
- Consider separate checkins table for scale

#### 3. **habit_events**

```sql
create table public.habit_events (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  habit_id uuid references public.habits not null,
  event_date date not null,
  completed boolean not null,
  created_at timestamp with time zone default now()
);
```

**Purpose**:
- Audit trail for all habit interactions
- Enables historical analysis
- Supports event sourcing patterns
- Allows for undo/redo functionality

#### 4. **blog_posts**

```sql
create table public.blog_posts (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  entry_id uuid references public.entries,
  title text not null,
  blocks jsonb not null,
  body_md text,
  status text not null default 'draft',
  created_at timestamp with time zone default now()
);
```

**Design Features**:
- **Optional entry_id**: Links to source journal entry
- **Dual content format**: Blocks and markdown
- **Status field**: Extensible for future states
- **Required title**: All posts must have titles

#### 5. **moods**

```sql
create table public.moods (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  rating integer not null,
  note text,
  created_at timestamp with time zone default now()
);
```

**Design Notes**:
- Separate from entries for flexibility
- Integer rating for quantitative analysis
- Optional note for context
- Time-series data structure

#### 6. **followers**

```sql
create table public.followers (
  follower_id uuid references auth.users not null,
  followed_id uuid references auth.users not null,
  timestamp timestamp with time zone default now(),
  primary key (follower_id, followed_id)
);
```

**Design Pattern**:
- Many-to-many self-referential relationship
- Composite primary key prevents duplicates
- Bidirectional queries supported
- Timestamp for relationship history

#### 7. **dashboard_layouts**

```sql
create table public.dashboard_layouts (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users unique not null,
  blocks jsonb,
  public_blocks jsonb,
  updated_at timestamp with time zone default now()
);
```

**Unique Aspects**:
- **One-to-one with users**: Unique constraint
- **Dual layout storage**: Dashboard and public profile
- **JSONB flexibility**: Easy layout evolution
- **Update tracking**: Know when last modified

#### 8. **weekly_summaries**

```sql
create table public.weekly_summaries (
  id uuid primary key default gen_random_uuid(),
  summary text not null,
  created_at timestamp with time zone default now()
);
```

**Observations**:
- No user_id field (design oversight?)
- System-wide summaries only
- Consider adding user association
- Potential for shared summaries

## JSONB Structures

### Block Structure (entries, blog_posts)

```json
{
  "text": "Block content here",
  "visibility": "private" | "public"
}
```

**Benefits**:
- Schema flexibility
- No migrations for new fields
- Efficient storage
- Native JSON operations

### Dashboard Block Structure

```json
{
  "id": "journal-1234567890",
  "type": "journal" | "habits" | "mood" | "insights" | "activity"
}
```

**Design**:
- Simple structure for easy manipulation
- Type-safe through application layer
- Extensible for new block types

## Security Model

### Row Level Security (RLS)

**Standard Pattern**:
```sql
-- Select: Users can only see their own data
create policy "select_own" on table_name
  for select using (auth.uid() = user_id);

-- Insert: Users can only insert their own data
create policy "insert_own" on table_name
  for insert with check (auth.uid() = user_id);

-- Update: Users can only update their own data
create policy "update_own" on table_name
  for update using (auth.uid() = user_id);
```

**Special Cases**:

1. **Followers Table**:
   - Can see relationships where user is participant
   - Can delete relationships they're part of

2. **Weekly Summaries**:
   - Public read access (no user restriction)
   - System-generated content

### Security Gaps

1. **Public Blog Posts**: No policy for anonymous access
2. **Public Profiles**: Need policies for public data
3. **API Routes**: Missing authentication checks

## Data Relationships

### Entity Relationship Model

```
auth.users
    │
    ├── entries (1:N)
    │     └── blog_posts (1:N, optional)
    │
    ├── habits (1:N)
    │     └── habit_events (1:N)
    │
    ├── moods (1:N)
    │
    ├── dashboard_layouts (1:1)
    │
    └── followers (N:M self-referential)
```

### Referential Integrity

All foreign keys use:
- `ON DELETE CASCADE` (implicit)
- Non-nullable references
- UUID type matching

## Performance Considerations

### Current Optimizations

1. **Indexes**:
   - Primary key indexes on all tables
   - Foreign key indexes (automatic)
   - Composite index on followers

2. **Query Patterns**:
   - User-scoped queries via RLS
   - Chronological ordering common
   - JSONB queries on blocks

### Recommended Indexes

```sql
-- Chronological queries
CREATE INDEX idx_entries_user_created 
  ON entries(user_id, created_at DESC);

-- Habit completion queries
CREATE INDEX idx_habit_events_habit_date 
  ON habit_events(habit_id, event_date DESC);

-- Blog post status queries
CREATE INDEX idx_blog_posts_user_status 
  ON blog_posts(user_id, status);
```

## Scalability Analysis

### Current Limitations

1. **Habits Checkins Array**:
   - Grows unbounded
   - No efficient partial updates
   - Consider separate table at scale

2. **JSONB Blocks**:
   - No partial updates
   - Full document replacement
   - Consider normalized structure for large entries

3. **Missing Pagination**:
   - All queries return full results
   - Memory issues at scale
   - Implement cursor-based pagination

### Scaling Strategies

1. **Partitioning**:
   - Partition by user_id for large tables
   - Time-based partitioning for events
   - Automatic with Supabase

2. **Archival**:
   - Move old entries to archive tables
   - Compress historical data
   - Separate read/write patterns

3. **Caching**:
   - Redis for frequently accessed data
   - Materialized views for analytics
   - Edge caching for public content

## Migration Patterns

### Schema Evolution

**JSONB Advantages**:
- Add fields without migrations
- Backward compatibility built-in
- Application-level validation

**Migration Strategy**:
1. Add new fields to JSONB first
2. Validate in application
3. Migrate to columns if needed
4. Use database migrations sparingly

## Data Integrity

### Constraints

**Current**:
- Foreign key constraints
- Not null constraints
- Unique constraints (dashboard_layouts)
- Primary key constraints

**Missing**:
- Check constraints on ranges
- Enum types for status fields
- Custom domain types

### Data Validation

**Application Level**:
- Zod schemas for type validation
- Business logic validation
- Sanitization before storage

**Database Level**:
- Basic type checking
- Referential integrity
- Unique constraints

## Backup and Recovery

### Supabase Features

1. **Automatic Backups**:
   - Daily backups retained 7-30 days
   - Point-in-time recovery
   - Geo-redundant storage

2. **Manual Backups**:
   - Export via Supabase CLI
   - pg_dump for full backups
   - Selective table exports

### Recovery Strategies

1. **Full Recovery**: Restore entire database
2. **Partial Recovery**: Restore specific tables
3. **User Recovery**: Restore single user's data

## Best Practices

### Development

1. **Always use transactions** for multi-table operations
2. **Test RLS policies** thoroughly
3. **Monitor query performance** with EXPLAIN
4. **Version control** schema changes
5. **Document** JSONB structures

### Production

1. **Regular backups** beyond Supabase defaults
2. **Monitor** database metrics
3. **Index** based on actual query patterns
4. **Archive** old data periodically
5. **Plan** for growth from day one

## Future Considerations

### Schema Enhancements

1. **Add user_id to weekly_summaries**
2. **Create analytics tables** for aggregated data
3. **Implement soft deletes** for recovery
4. **Add audit log table** for compliance
5. **Create notification tables** for future features

### Performance Improvements

1. **Implement materialized views** for dashboards
2. **Add full-text search** indexes
3. **Create summary tables** for reporting
4. **Optimize JSONB** with GIN indexes
5. **Consider TimescaleDB** for time-series data

## Conclusion

The LifeSync database design provides a solid foundation with good security practices and flexible schema design. The use of JSONB provides adaptability, while foreign keys ensure data integrity. Key areas for improvement include implementing proper public access policies, optimizing for scale, and adding missing indexes. The architecture supports the current feature set well while leaving room for growth.