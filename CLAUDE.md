# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LifeSync AI is an AI-powered personal life management app built with Next.js 15, TypeScript, Supabase, and Google Genkit (Gemini 2.0). It features journaling, habit tracking, AI insights, and social features.

## Essential Commands

```bash
# Development
npm run dev          # Start Next.js dev server (port 9002)
npm run genkit:dev   # Start Genkit AI dev server
npm run genkit:watch # Start Genkit with hot reload

# Quality Checks (run before committing)
npm run lint         # Run ESLint
npm run typecheck    # TypeScript type checking
npm test             # Run all tests
npm test:watch       # Run tests in watch mode

# Build & Production
npm run build        # Create production build
npm run start        # Start production server
```

## Architecture Overview

### Tech Stack
- **Frontend**: Next.js 15 App Router with TypeScript, React 18, Tailwind CSS
- **UI Components**: Custom components based on shadcn/ui patterns in `src/components/ui/`
- **Backend**: Supabase (PostgreSQL) for auth, data, and real-time features
- **AI**: Google Genkit with Gemini 2.0 Flash for chat, insights, and content generation
- **Testing**: Jest + React Testing Library with mocks for Supabase and icons

### Key Architectural Patterns

1. **Route Groups**: App Router uses route groups for layout organization:
   - `(auth)` - Login/register pages
   - `(main)` - Core app features (dashboard, journal, habits, etc.)
   - `(onboarding)` - Multi-step onboarding flow

2. **AI Flows**: All AI functionality is in `src/ai/flows/`:
   - Each flow is a discrete Genkit function (conversation, blog ideas, insights, etc.)
   - API routes in `src/app/api/` wrap these flows for client consumption

3. **Data Layer**:
   - Supabase client hooks in `src/hooks/` for data fetching
   - Type definitions in `src/lib/types.ts`
   - Database schema includes: entries, habits, moods, blog_posts, weekly_summaries, followers

4. **Component Structure**:
   - Reusable UI components follow shadcn/ui patterns
   - Layout components (AppShell, SidebarNav) handle app structure
   - Feature components are colocated with their pages

### Database Schema Highlights

- **entries**: Journal entries stored as JSONB blocks with mood and tags
- **habits**: Track frequency (daily/weekly/monthly) with streak calculations
- **habit_events**: Individual habit completion records
- **blog_posts**: Public posts linked to journal entries
- **dashboard_layouts**: Persisted user dashboard configurations

### Testing Strategy

Tests are organized by feature in `/tests/` directory. All new features should include tests for:
- Component rendering and user interactions
- Hook behavior and state management
- API route functionality

## Development Guidelines

### Path Aliases
Use `@/` for imports from the `src/` directory:
```typescript
import { Button } from '@/components/ui/button'
```

### Environment Variables
Required variables (see `.env.example`):
- `GENKIT_API_KEY` - Google AI API key
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anon key

### Common Patterns

1. **Creating New Pages**: Add to appropriate route group in `src/app/`
2. **Adding AI Features**: Create flow in `src/ai/flows/`, then API route in `src/app/api/`
3. **UI Components**: Follow existing patterns in `src/components/ui/` - most are shadcn/ui based
4. **Data Fetching**: Use hooks pattern from `src/hooks/` for Supabase queries

### Design System
- Primary color: #5A8DEE (desaturated blue)
- Fonts: Space Grotesk (headlines), Inter (body)
- Layout: Card-based with consistent spacing