import { renderHook } from '@testing-library/react'
import { useOnboardingProgress } from '@/hooks/use-onboarding-progress'

const usePathnameMock = jest.fn()

jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn(), replace: jest.fn(), prefetch: jest.fn() }),
  usePathname: () => usePathnameMock(),
}))

beforeEach(() => {
  localStorage.clear()
})

test('updates localStorage as user navigates through onboarding', () => {
  usePathnameMock.mockReturnValue('/onboarding/welcome')
  const { result, rerender } = renderHook(() => useOnboardingProgress())
  expect(result.current.currentStep).toBe(1)
  expect(localStorage.getItem('onboardingStep')).toBe('1')

  usePathnameMock.mockReturnValue('/onboarding/privacy')
  rerender()
  expect(result.current.currentStep).toBe(2)
  expect(localStorage.getItem('onboardingStep')).toBe('2')

  usePathnameMock.mockReturnValue('/onboarding/ai-personality')
  rerender()
  expect(result.current.currentStep).toBe(3)
  expect(localStorage.getItem('onboardingStep')).toBe('3')

  usePathnameMock.mockReturnValue('/onboarding/first-entry')
  rerender()
  expect(result.current.currentStep).toBe(4)
  expect(localStorage.getItem('onboardingStep')).toBe('4')

  usePathnameMock.mockReturnValue('/onboarding/tutorial')
  rerender()
  expect(result.current.currentStep).toBe(5)
  expect(localStorage.getItem('onboardingStep')).toBe('5')
})
