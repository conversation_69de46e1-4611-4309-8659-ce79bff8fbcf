import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import EditBlogPostPage from '@/app/(main)/blog/edit/[id]/page'
import { getPost, updatePost } from '@/lib/blog'

const useParamsMock = jest.fn()

jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn(), replace: jest.fn(), prefetch: jest.fn() }),
  useParams: () => useParamsMock(),
}))

jest.mock('@/lib/blog', () => ({
  getPosts: jest.fn(),
  addPost: jest.fn(),
  getPost: jest.fn(),
  updatePost: jest.fn(),
}))

const mockedGetPost = getPost as jest.MockedFunction<typeof getPost>
const mockedUpdatePost = updatePost as jest.MockedFunction<typeof updatePost>

test('edits existing blog post', async () => {
  useParamsMock.mockReturnValue({ id: '1' })
  mockedGetPost.mockResolvedValue({ id: '1', title: 'Old', body: 'Old body', status: 'draft' })
  mockedUpdatePost.mockResolvedValue(undefined as any)
  render(<EditBlogPostPage />)
  await userEvent.clear(screen.getByLabelText(/title/i))
  await userEvent.type(screen.getByLabelText(/title/i), 'Updated')
  await userEvent.click(screen.getByRole('button', { name: /save post/i }))
  expect(mockedUpdatePost).toHaveBeenCalledWith(
    expect.objectContaining({ id: '1', title: 'Updated' })
  )
})

