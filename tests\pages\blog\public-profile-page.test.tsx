import { render, screen } from '@testing-library/react'
import PublicProfilePage from '@/app/p/[username]/page'
import { getPosts } from '@/lib/blog'

const useParamsMock = jest.fn()

jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn(), replace: jest.fn(), prefetch: jest.fn() }),
  useParams: () => useParamsMock(),
}))

jest.mock('@/lib/blog', () => ({
  getPosts: jest.fn(),
  addPost: jest.fn(),
  getPost: jest.fn(),
  updatePost: jest.fn(),
}))

const mockedGetPosts = getPosts as jest.MockedFunction<typeof getPosts>

test('shows only public posts', async () => {
  useParamsMock.mockReturnValue({ username: 'alice' })
  mockedGetPosts.mockResolvedValue([
    { id: '1', title: 'Public Post', body: 'x', status: 'public' },
    { id: '2', title: 'Draft Post', body: 'y', status: 'draft' },
  ])
  render(<PublicProfilePage />)
  expect(screen.getByText(/alice's Public Posts/i)).toBeInTheDocument()
  expect(screen.getByText('Public Post')).toBeInTheDocument()
  expect(screen.queryByText('Draft Post')).toBeNull()
})

