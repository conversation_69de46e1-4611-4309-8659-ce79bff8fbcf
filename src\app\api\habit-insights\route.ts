import { NextResponse } from 'next/server';
import { generateHabitInsights } from '@/ai/flows/generate-habit-insights';
import { requireAuth, createServerSupabaseClient, createErrorResponse } from '@/lib/auth-server';
import { aiRateLimiter, createRateLimitResponse } from '@/lib/rate-limit';
import { startOfWeek, endOfWeek, parseISO, isWithinInterval } from 'date-fns';

export async function GET() {
  try {
    // Require authentication
    const session = await requireAuth();
    
    // Apply rate limiting
    const rateLimitResult = await aiRateLimiter.check(session.user.id);
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult);
    }
    
    // Create authenticated Supabase client
    const supabase = await createServerSupabaseClient();
    
    // Fetch user's habits
    const { data, error } = await supabase
      .from('habits')
      .select('name, frequency, checkins')
      .eq('user_id', session.user.id);

    if (error) {
      console.error('Failed to load habits', error);
      throw new Error('Failed to load habits');
    }

    // Calculate this week's check-ins
    const now = new Date();
    const weekStart = startOfWeek(now, { weekStartsOn: 1 }); // Monday
    const weekEnd = endOfWeek(now, { weekStartsOn: 1 });
    
    const habits = (data || []).map(h => {
      const weeklyCheckins = h.checkins.filter(dateStr => {
        try {
          const date = parseISO(dateStr);
          return isWithinInterval(date, { start: weekStart, end: weekEnd });
        } catch {
          return false;
        }
      }).length;
      
      return `${h.name}: ${weeklyCheckins}/${h.frequency}`;
    });

    if (habits.length === 0) {
      return NextResponse.json({ insights: 'Start tracking habits to receive personalized insights!' });
    }

    // Generate insights
    const insights = await generateHabitInsights({ habits });
    
    return NextResponse.json({ insights });
  } catch (error) {
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    console.error('Failed to generate habit insights', error);
    return createErrorResponse('Failed to generate habit insights', 500);
  }
}
