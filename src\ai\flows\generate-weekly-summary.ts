'use server';

/**
 * @fileOverview A flow to summarize the week's journal entries.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';

const GenerateWeeklySummaryInputSchema = z.object({
  entries: z.array(z.string()).describe('Journal entry texts from the past week'),
});
export type GenerateWeeklySummaryInput = z.infer<typeof GenerateWeeklySummaryInputSchema>;

const GenerateWeeklySummaryOutputSchema = z
  .string()
  .describe('A concise recap of the week highlighting key themes.');
export type GenerateWeeklySummaryOutput = z.infer<typeof GenerateWeeklySummaryOutputSchema>;

export async function generateWeeklySummary(
  input: GenerateWeeklySummaryInput,
): Promise<GenerateWeeklySummaryOutput> {
  return generateWeeklySummaryFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateWeeklySummaryPrompt',
  input: { schema: GenerateWeeklySummaryInputSchema },
  output: { schema: GenerateWeeklySummaryOutputSchema },
  prompt: `You are a journaling assistant. Using the provided journal entries, write a short weekly recap for the user summarizing recurring themes, accomplishments and challenges.`,
});

const generateWeeklySummaryFlow = ai.defineFlow(
  {
    name: 'generateWeeklySummaryFlow',
    inputSchema: GenerateWeeklySummaryInputSchema,
    outputSchema: GenerateWeeklySummaryOutputSchema,
  },
  async ({ entries }) => {
    const { output } = await prompt({ entries: entries.join('\n\n') });
    return output!;
  },
);
