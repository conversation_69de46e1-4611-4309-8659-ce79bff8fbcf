"use client";

import { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface Message {
  sender: 'user' | 'assistant';
  text: string;
}

export default function AIChatPage() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');

  const sendMessage = async () => {
    if (!input.trim()) return;
    const text = input;
    setMessages(prev => [...prev, { sender: 'user', text }]);
    setInput('');
    const res = await fetch('/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        history: messages.map(m => m.text),
        message: text,
      }),
    });
    if (res.ok) {
      const data = await res.json();
      setMessages(prev => [...prev, { sender: 'assistant', text: data.reply }]);
    }
  };

  return (
    <div className="container mx-auto py-2">
      <h1 className="text-3xl font-bold font-headline text-foreground mb-8">
        AI Chat
      </h1>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="font-headline">Ask anything</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 min-h-[300px]">
          {messages.map((m, idx) => (
            <p key={idx} className={m.sender === 'user' ? 'text-right' : ''}>
              {m.text}
            </p>
          ))}
        </CardContent>
        <CardFooter className="flex gap-2">
          <Input
            placeholder="Say something..."
            value={input}
            onChange={e => setInput(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
              }
            }}
          />
          <Button onClick={sendMessage}>Send</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
