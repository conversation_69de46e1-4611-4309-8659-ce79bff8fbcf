
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, <PERSON><PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, BookOpen, Target, Edit3, PackagePlus } from "lucide-react"; // Changed PlusCircle to Edit3 for "My Trackers"
import Image from "next/image";

export default function CollectionsPage() {
  return (
    <div className="container mx-auto py-2">
      <h1 className="text-3xl font-bold mb-2 font-headline text-foreground">Collections & Trackers</h1>
      <p className="text-muted-foreground mb-8">
        Organize and track various aspects of your life. Create custom trackers for anything you can imagine, 
        or use some of our example templates below.
      </p>
      <Tabs defaultValue="my-trackers" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 mb-6">
          <TabsTrigger value="my-trackers">
            <PackagePlus className="mr-2 h-4 w-4" /> My Trackers
          </TabsTrigger>
          <TabsTrigger value="habits">
            <CheckCircle className="mr-2 h-4 w-4" /> Habits (Example)
          </TabsTrigger>
          <TabsTrigger value="books">
            <BookOpen className="mr-2 h-4 w-4" /> Books (Example)
          </TabsTrigger>
          <TabsTrigger value="okrs">
            <Target className="mr-2 h-4 w-4" /> OKRs (Example)
          </TabsTrigger>
        </TabsList>

        <TabsContent value="my-trackers">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">My Custom Trackers</CardTitle>
              <CardDescription>
                This is where your personalized collections and trackers will live. 
                Define what you want to track, and how you want to see it. 
                Think workouts, mood, project progress, learning logs, and more!
              </CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x200.png" alt="Custom trackers placeholder" width={300} height={200} className="rounded-md mb-4" data-ai-hint="dashboard charts" />
              <p>Functionality to create and manage custom trackers coming soon!</p>
              <p className="text-sm mt-2">For now, use the Rapid Log on the 'Today' page to add entries with custom categories.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="habits">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Example: Habit Tracker</CardTitle>
              <CardDescription>Monitor and build your daily habits. You can create trackers like this yourself!</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x200.png" alt="Habit tracker placeholder" width={300} height={200} className="rounded-md mb-4" data-ai-hint="charts graphs" />
              <p>Example habit tracking functionality.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="books">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Example: Book Log</CardTitle>
              <CardDescription>Keep track of books you've read or want to read. Another potential custom tracker!</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x200.png" alt="Book log placeholder" width={300} height={200} className="rounded-md mb-4" data-ai-hint="books library" />
              <p>Example book logging feature.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="okrs">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="font-headline">Example: OKRs</CardTitle>
              <CardDescription>Set and track your goals effectively using a custom OKR tracker.</CardDescription>
            </CardHeader>
            <CardContent className="min-h-[200px] flex flex-col items-center justify-center text-muted-foreground">
              <Image src="https://placehold.co/300x200.png" alt="OKRs placeholder" width={300} height={200} className="rounded-md mb-4" data-ai-hint="goals targets" />
              <p>Example OKR tracking functionality.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
