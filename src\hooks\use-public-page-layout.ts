'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase-server'

export interface PublicPageBlock {
  id: string
  type: string
}

const STORAGE_KEY = 'lifesync.public.layout'

export function usePublicPageLayout() {
  const [blocks, setBlocks] = useState<PublicPageBlock[]>([])

  useEffect(() => {
    const load = async () => {
      try {
        const { data } = await supabase
          .from('dashboard_layouts')
          .select('public_blocks')
          .single()

        if (data?.public_blocks) {
          setBlocks(data.public_blocks as PublicPageBlock[])
          return
        }
      } catch {
        // ignore and fall back to local storage
      }

      try {
        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          setBlocks(JSON.parse(stored) as PublicPageBlock[])
        } else {
          setBlocks([{ id: Date.now().toString(), type: 'posts' }])
        }
      } catch {
        // ignore
      }
    }
    load()
  }, [])

  useEffect(() => {
    if (blocks.length === 0) return
    localStorage.setItem(STORAGE_KEY, JSON.stringify(blocks))
    supabase
      .from('dashboard_layouts')
      .upsert({ public_blocks: blocks })
      .catch(() => {})
  }, [blocks])

  const addBlock = (type: string) => {
    const block: PublicPageBlock = { id: Date.now().toString(), type }
    setBlocks(prev => [...prev, block])
  }

  const removeBlock = (id: string) => {
    setBlocks(prev => prev.filter(b => b.id !== id))
  }

  const moveBlock = (oldIndex: number, newIndex: number) => {
    setBlocks(prev => {
      const copy = [...prev]
      const [moved] = copy.splice(oldIndex, 1)
      copy.splice(newIndex, 0, moved)
      return copy
    })
  }

  return { blocks, addBlock, removeBlock, moveBlock, setBlocks }
}
