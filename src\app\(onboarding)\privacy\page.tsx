
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ShieldCheck } from "lucide-react";
import Link from "next/link";

export default function PrivacySetupPage() {
  return (
    <Card className="w-full shadow-xl">
      <CardHeader className="text-center">
        <ShieldCheck className="h-12 w-12 text-primary mx-auto mb-2" />
        <CardTitle className="text-3xl font-headline">Privacy First</CardTitle>
        <CardDescription className="text-lg">
          Your data, your rules. Let's set up your privacy preferences.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="font-semibold mb-2 text-foreground">Default Visibility</h3>
          <p className="text-sm text-muted-foreground mb-3">Choose who can see your entries and stats by default. You can change this per item.</p>
          <RadioGroup defaultValue="private">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="private" id="private" />
              <Label htmlFor="private">Private (Only you)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="friends" id="friends" />
              <Label htmlFor="friends">Friends Only (Future feature)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="public" id="public" />
              <Label htmlFor="public">Public (Not recommended for personal data)</Label>
            </div>
          </RadioGroup>
        </div>
        <div>
          <h3 className="font-semibold mb-2 text-foreground">AI Data Processing</h3>
           <p className="text-sm text-muted-foreground mb-3">Allow LifeSync AI to process your anonymized data to provide insights and improve its services.</p>
          <RadioGroup defaultValue="allow">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="allow" id="ai-allow" />
              <Label htmlFor="ai-allow">Allow AI Processing (Recommended for full features)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="disallow" id="ai-disallow" />
              <Label htmlFor="ai-disallow">Disallow AI Processing (Some features may be limited)</Label>
            </div>
          </RadioGroup>
        </div>
         <p className="text-xs text-muted-foreground text-center pt-4">
            You can change these settings anytime in your profile. 
            LifeSync AI uses strong encryption to protect your data.
          </p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" asChild>
            <Link href="/onboarding/welcome">Back</Link>
        </Button>
        <Button asChild size="lg">
          <Link href="/onboarding/ai-personality">Next: AI Personality</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
