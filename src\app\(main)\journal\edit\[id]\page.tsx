"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { ChevronLeft, Save } from "lucide-react";
import Link from "next/link";
import { getEntry, updateEntry } from "@/lib/journal";
import { Block } from "@/lib/types";

import { MoodSelector } from "@/components/mood-selector";

export default function EditJournalEntryPage() {
  const router = useRouter();
  const params = useParams<{ id: string }>();
  const entryId = params.id;
  const [title, setTitle] = useState("");
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [mood, setMood] = useState("");
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    getEntry(entryId).then((existing) => {
      if (existing) {
        setTitle(existing.title);
        setBlocks(existing.blocks);
        setMood(existing.mood || "");
      }
      setLoaded(true);
    });
  }, [entryId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await updateEntry(entryId, title, blocks, mood);
    router.push("/journal");
  };

  const updateBlock = (index: number, field: keyof Block, value: string) => {
    setBlocks((prev) => {
      const copy = [...prev];
      copy[index] = { ...copy[index], [field]: value };
      return copy;
    });
  };

  const addBlockField = () => {
    setBlocks((prev) => [...prev, { text: "", visibility: "private" }]);
  };

  const removeBlock = (idx: number) => {
    setBlocks((prev) => prev.filter((_, i) => i !== idx));
  };

  if (!loaded) return null;

  return (
    <div className="container mx-auto py-2">
      <Link href="/journal" className="inline-flex items-center text-sm text-primary hover:underline mb-4">
        <ChevronLeft className="mr-1 h-4 w-4" />
        Back to Journal
      </Link>
      <Card className="w-full shadow-xl">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle className="text-2xl font-headline">Edit Journal Entry</CardTitle>
            <CardDescription>Edit your thoughts and reflections.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="entry-title" className="text-base">Title (Optional)</Label>
              <Input
                id="entry-title"
                placeholder="A memorable day..."
                className="mt-1 text-base"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>
            <div className="space-y-4">
              <Label className="text-base">Blocks</Label>
              {blocks.map((block, i) => (
                <div key={i} className="space-y-2 border p-2 rounded">
                  <Textarea
                    placeholder="Write anything that comes to mind..."
                    className="min-h-[120px] text-base"
                    value={block.text}
                    onChange={(e) => updateBlock(i, 'text', e.target.value)}
                  />
                  <div className="flex items-center gap-2">
                    <Select
                      value={block.visibility}
                      onValueChange={(v) => updateBlock(i, 'visibility', v)}
                    >
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="Visibility" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="private">Private</SelectItem>
                        <SelectItem value="public">Public</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeBlock(i)}
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
              <Button type="button" variant="secondary" size="sm" onClick={addBlockField}>
                Add Block
              </Button>
            </div>
            <div>
              <Label htmlFor="mood-selector" className="text-base">Mood</Label>
              <div className="flex items-center gap-2 mt-1">
                <MoodSelector value={mood} onChange={setMood} />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button variant="outline" asChild>
              <Link href="/journal">Cancel</Link>
            </Button>
            <Button type="submit">
              <Save className="mr-2 h-4 w-4" />
              Save Entry
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

