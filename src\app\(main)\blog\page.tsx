"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useBlogPosts } from "@/hooks/use-blog-posts";

export default function BlogPage() {
  const { posts } = useBlogPosts();

  return (
    <div className="container mx-auto py-2">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
        <h1 className="text-3xl font-bold font-headline text-foreground">My Blog</h1>
        <div className="flex gap-2 w-full sm:w-auto">
          <div className="relative flex-grow sm:flex-grow-0">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search posts..."
              className="pl-8 sm:w-[200px] md:w-[300px]"
              disabled
            />
          </div>
          <Button asChild>
            <Link href="/blog/new">
              <PlusCircle className="mr-2 h-4 w-4" /> New Post
            </Link>
          </Button>
        </div>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="font-headline">Blog Posts</CardTitle>
          <CardDescription>
            Share your experiences and reflections with others.
          </CardDescription>
        </CardHeader>
        <CardContent className="min-h-[300px]">
          {posts.length === 0 ? (
            <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
              <p className="text-lg">No blog posts yet.</p>
              <p className="text-sm mt-2">Click \"New Post\" to start writing.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {posts.map((post) => (
                <div key={post.id} className="border-b pb-2 flex items-center justify-between">
                  <div>
                    <Link href={`/blog/${post.id}`} className="font-medium hover:underline">
                      {post.title || "Untitled Post"}
                    </Link>
                    <p className="text-sm text-muted-foreground capitalize">{post.status}</p>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/blog/edit/${post.id}`}>Edit</Link>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
